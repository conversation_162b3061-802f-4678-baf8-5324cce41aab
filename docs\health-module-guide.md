# 健康数据管理模块使用指南

## 概述

本模块为 HealthModal.vue 组件提供了完整的健康数据管理功能，包括体重、饮食、运动记录的增删改查操作，并将数据持久化存储到 Dexie 数据库中。

## 功能特性

### 1. 数据存储
- 使用 Dexie.js 进行本地数据库存储
- 支持离线数据访问
- 数据结构包括：日期、星期、餐食、运动、体重、备注等

### 2. 界面功能
- **查看记录**：以表格形式展示所有健康记录
- **新增记录**：通过模态框添加新的健康数据
- **编辑记录**：修改现有的健康记录
- **删除记录**：删除不需要的记录
- **搜索功能**：支持按日期、备注、餐食内容搜索
- **体重统计**：可视化体重变化趋势图表

### 3. 数据管理
- 自动初始化默认数据
- 支持批量数据导入
- 数据验证和错误处理
- 实时数据同步

## 文件结构

```
src/
├── components/
│   └── HealthModal.vue          # 主界面组件
├── lib/
│   └── healthDB.js             # 数据库操作模块
├── test/
│   └── health-db-test.js       # 测试文件
└── docs/
    └── health-module-guide.md  # 使用指南
```

## 数据库结构

### healthRecords 表
```javascript
{
  id: number,           // 自增主键
  date: string,         // 日期 (如: '6.5')
  day: string,          // 星期 (如: '四')
  meals: Array,         // 餐食记录
  exercise: string,     // 运动记录
  weight: string,       // 体重
  notes: string,        // 备注
  createdAt: Date,      // 创建时间
  updatedAt: Date       // 更新时间
}
```

### meals 数组结构
```javascript
[
  { type: '早餐', food: '食物内容' },
  { type: '午饭', food: '食物内容' },
  { type: '晚上', food: '食物内容' }
]
```

## API 接口

### healthDBOperations 对象提供以下方法：

#### 1. 添加记录
```javascript
await healthDBOperations.addHealthRecord(record)
```

#### 2. 更新记录
```javascript
await healthDBOperations.updateHealthRecord(id, updates)
```

#### 3. 删除记录
```javascript
await healthDBOperations.deleteHealthRecord(id)
```

#### 4. 获取所有记录
```javascript
const records = await healthDBOperations.getAllHealthRecords()
```

#### 5. 根据日期获取记录
```javascript
const record = await healthDBOperations.getHealthRecordByDate(date)
```

#### 6. 批量导入记录
```javascript
await healthDBOperations.bulkImportHealthRecords(records)
```

#### 7. 获取体重统计
```javascript
const stats = await healthDBOperations.getWeightStats()
```

#### 8. 清空所有记录
```javascript
await healthDBOperations.clearAllHealthRecords()
```

## 使用方法

### 1. 在组件中使用
```javascript
import { healthDBOperations } from '../lib/healthDB.js'

// 加载数据
const loadData = async () => {
  const records = await healthDBOperations.getAllHealthRecords()
  // 处理数据...
}

// 保存数据
const saveData = async (record) => {
  await healthDBOperations.addHealthRecord(record)
}
```

### 2. 界面操作
- 点击"新增记录"按钮添加新数据
- 点击表格行中的编辑按钮修改数据
- 点击删除按钮删除数据
- 使用搜索框过滤记录
- 点击"体重统计"查看图表分析

## 测试

运行测试文件验证功能：
```javascript
import { testHealthDB, cleanupTestData } from '../test/health-db-test.js'

// 运行测试
await testHealthDB()

// 清理测试数据
await cleanupTestData()
```

## 注意事项

1. **数据持久化**：数据存储在浏览器本地，清除浏览器数据会丢失记录
2. **数据格式**：确保日期格式统一（如：'6.5'）
3. **错误处理**：所有数据库操作都包含错误处理机制
4. **性能优化**：大量数据时建议分页加载
5. **浏览器兼容**：需要支持 IndexedDB 的现代浏览器

## 扩展功能

可以考虑添加的功能：
- 数据导出/导入功能
- 更多统计图表类型
- 数据备份到云端
- 提醒和通知功能
- 数据分析和建议

## 技术栈

- **Vue 3**: 前端框架
- **Dexie.js**: IndexedDB 封装库
- **ECharts**: 图表库
- **Lucide Vue**: 图标库
