<template>
  <!-- Password Vault Modal -->
  <Transition name="modal-fade">
    <div
      v-if="show"
      class="password-vault-overlay"
      @click.self="$emit('close')"
    >
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- <el-menu
            active-text-color="#ffd04b"
            background-color="#545c64"
            class="el-menu-vertical-demo"
            default-active="2"
            text-color="#fff"
            @open="handleOpen"
            @close="handleClose"
          >
            <el-menu-item index="1">
              <el-icon><icon-menu /></el-icon>
              <span>菜品管理</span>
            </el-menu-item>
            <el-menu-item index="2">
              <el-icon><icon-menu /></el-icon>
              <span>Navigator Two</span>
            </el-menu-item>
            <el-menu-item index="3" disabled>
              <el-icon><document /></el-icon>
              <span>Navigator Three</span>
            </el-menu-item>
            <el-menu-item index="4">
              <el-icon><setting /></el-icon>
              <span>Navigator Four</span>
            </el-menu-item>
          </el-menu> -->
          <!-- Vault particles -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle" />
          </div>

          <!-- Table CRUD Content -->
          <div class="table-content">
            <div class="table-header">
              <div class="search-add-container">
                <el-input
                  v-model="searchText"
                  placeholder="搜索菜谱..."
                  class="search-input"
                  :prefix-icon="Search"
                  @input="handleSearch"
                />
                <el-button
                  type="primary"
                  class="add-btn"
                  @click="handleAdd"
                  :icon="Plus"
                >
                  添加菜谱
                </el-button>
              </div>
            </div>

            <!-- Table -->
            <div class="table-wrapper">
              <el-table
                :data="filteredTableData"
                class="custom-table"
                stripe
                border
                style="width: 100%"
                v-loading="loading"
                element-loading-text="加载中..."
                element-loading-background="rgba(0, 0, 0, 0.8)"
                :header-cell-style="{
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: '#fff',
                  fontWeight: 'bold',
                }"
                :cell-style="{
                  background: 'rgba(255, 255, 255, 0.05)',
                  color: '#fff',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }"
              >
                <el-table-column
                  prop="id"
                  label="ID"
                  width="80"
                  align="center"
                />
                <el-table-column prop="name" label="菜名" width="150" />
                <el-table-column
                  prop="description"
                  label="描述"
                  width="200"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="category"
                  label="分类"
                  width="100"
                  align="center"
                >
                  <template #default="scope">
                    <el-tag :type="getCategoryType(scope.row.category)">
                      {{ getCategoryName(scope.row.category) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="difficulty"
                  label="难度"
                  width="100"
                  align="center"
                >
                  <template #default="scope">
                    <el-tag :type="getDifficultyType(scope.row.difficulty)">
                      {{ getDifficultyText(scope.row.difficulty) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="cookTime"
                  label="制作时间"
                  width="100"
                  align="center"
                >
                  <template #default="scope">
                    {{ scope.row.cookTime }}分钟
                  </template>
                </el-table-column>
                <el-table-column
                  prop="rating"
                  label="评分"
                  width="120"
                  align="center"
                >
                  <template #default="scope">
                    <div class="rating-display">
                      <span v-for="i in 5" :key="i" class="heart-rating">
                        {{ i <= scope.row.rating ? "❤️" : "🤍" }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="isFavorite"
                  label="收藏"
                  width="80"
                  align="center"
                >
                  <template #default="scope">
                    <el-tag :type="scope.row.isFavorite ? 'success' : 'info'">
                      {{ scope.row.isFavorite ? "是" : "否" }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" fixed="right">
                  <template #default="scope">
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleEdit(scope.row)"
                      :icon="Edit"
                      circle
                      class="action-btn"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(scope.row)"
                      :icon="Trash2"
                      circle
                      class="action-btn"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="totalData"
                layout="total, sizes, prev, pager, next, jumper"
                class="custom-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>

          <!-- Add/Edit Dialog -->
          <el-dialog
            v-model="dialogVisible"
            :title="dialogTitle"
            width="800px"
            class="custom-dialog"
            :before-close="handleDialogClose"
          >
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="100px"
              class="dialog-form"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="菜名" prop="name">
                    <el-input
                      v-model="formData.name"
                      placeholder="请输入菜名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分类" prop="category">
                    <el-select
                      v-model="formData.category"
                      placeholder="请选择分类"
                      style="width: 100%"
                    >
                      <el-option label="家常菜" value="home" />
                      <el-option label="拿手菜" value="specialty" />
                      <el-option label="甜品" value="dessert" />
                      <el-option label="汤羹" value="soup" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="描述" prop="description">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入菜品描述"
                />
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="制作时间" prop="cookTime">
                    <el-input-number
                      v-model="formData.cookTime"
                      :min="1"
                      :max="300"
                      placeholder="分钟"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="难度" prop="difficulty">
                    <el-select
                      v-model="formData.difficulty"
                      placeholder="请选择难度"
                      style="width: 100%"
                    >
                      <el-option label="简单" value="easy" />
                      <el-option label="中等" value="medium" />
                      <el-option label="挑战" value="hard" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="份量" prop="servings">
                    <el-input-number
                      v-model="formData.servings"
                      :min="1"
                      :max="20"
                      placeholder="人份"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="评分" prop="rating">
                    <el-rate v-model="formData.rating" :max="5" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="收藏">
                    <el-switch v-model="formData.isFavorite" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="标签">
                <el-input
                  v-model="tagsInput"
                  placeholder="请输入标签，用逗号分隔"
                  @blur="updateTags"
                />
                <div
                  class="tags-display"
                  v-if="formData.tags && formData.tags.length"
                >
                  <el-tag
                    v-for="(tag, index) in formData.tags"
                    :key="index"
                    closable
                    @close="removeTag(index)"
                    style="margin: 2px"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </el-form-item>

              <el-form-item label="故事">
                <el-input
                  v-model="formData.story"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入这道菜的故事..."
                />
              </el-form-item>

              <!-- 图片上传 -->
              <el-form-item label="主图片">
                <el-upload
                  class="image-uploader"
                  :show-file-list="false"
                  :before-upload="beforeImageUpload"
                  :on-success="handleImageSuccess"
                  accept="image/*"
                >
                  <img
                    v-if="formData.image"
                    :src="formData.image"
                    class="uploaded-image"
                  />
                  <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>

              <!-- 食材管理 -->
              <el-form-item label="食材">
                <div class="ingredients-manager">
                  <div
                    v-for="(ingredient, index) in formData.ingredients"
                    :key="index"
                    class="ingredient-item"
                  >
                    <el-input
                      v-model="ingredient.name"
                      placeholder="食材名称"
                      style="width: 200px; margin-right: 10px"
                    />
                    <el-input
                      v-model="ingredient.amount"
                      placeholder="用量"
                      style="width: 120px; margin-right: 10px"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeIngredient(index)"
                      :icon="Minus"
                      circle
                    />
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    @click="addIngredient"
                    :icon="Plus"
                  >
                    添加食材
                  </el-button>
                </div>
              </el-form-item>

              <!-- 制作步骤 -->
              <el-form-item label="制作步骤">
                <div class="steps-manager">
                  <div
                    v-for="(step, index) in formData.steps"
                    :key="index"
                    class="step-item"
                  >
                    <div class="step-number">{{ index + 1 }}</div>
                    <div class="step-content">
                      <el-input
                        v-model="step.description"
                        type="textarea"
                        :rows="2"
                        placeholder="步骤描述"
                        style="margin-bottom: 10px"
                      />
                      <el-input
                        v-model="step.tip"
                        placeholder="小贴士（可选）"
                        style="margin-bottom: 10px"
                      />
                      <el-upload
                        class="step-image-uploader"
                        :show-file-list="false"
                        :before-upload="
                          (file) => beforeStepImageUpload(file, index)
                        "
                        accept="image/*"
                      >
                        <img
                          v-if="step.image"
                          :src="step.image"
                          class="step-uploaded-image"
                        />
                        <el-button v-else size="small" type="primary"
                          >上传步骤图片</el-button
                        >
                      </el-upload>
                    </div>
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeStep(index)"
                      :icon="Minus"
                      circle
                      style="margin-left: 10px"
                    />
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    @click="addStep"
                    :icon="Plus"
                  >
                    添加步骤
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="handleDialogClose">取消</el-button>
              <el-button
                type="primary"
                @click="handleSave"
                :loading="saveLoading"
              >
                {{ editingId ? "更新菜谱" : "保存菜谱" }}
              </el-button>
            </template>
          </el-dialog>

          <!-- Close button -->
          <button @click="$emit('close')" class="vault-close-btn">
            <X class="vault-icon" />
          </button>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from "vue";
import {
  X,
  Database,
  Search,
  Plus,
  Edit,
  Trash2,
  Minus,
} from "lucide-vue-next";
import { ElMessage, ElMessageBox } from "element-plus";
import { recipeDB } from "@/lib/db.js";
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from "@element-plus/icons-vue";
// Props
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

// Emits
defineEmits(["close"]);

// Table data - 从数据库加载
const tableData = ref([]);
const loading = ref(false);

// Search and pagination
const searchText = ref("");
const currentPage = ref(1);
const pageSize = ref(10);

// Computed properties
/**
 * 分页显示的表格数据
 * 由于搜索已经在数据库层面处理，这里只需要处理分页
 */
const filteredTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});
// const handleOpen = (key: string, keyPath: string[]) => {
//   console.log(key, keyPath)
// }
// const handleClose = (key: string, keyPath: string[]) => {
//   console.log(key, keyPath)
// }
/**
 * 数据总数（用于分页组件）
 */
const totalData = computed(() => {
  return tableData.value.length;
});

// Dialog
const dialogVisible = ref(false);
const dialogTitle = computed(() => (editingId.value ? "编辑菜谱" : "添加菜谱"));
const editingId = ref(null);
const saveLoading = ref(false);

// Form
const formRef = ref();
const tagsInput = ref("");
const formData = reactive({
  name: "",
  description: "",
  category: "",
  image: "",
  images: [""],
  cookTime: 30,
  difficulty: "easy",
  servings: 2,
  rating: 5,
  isFavorite: false,
  spicy: "",
  tags: [],
  story: "",
  ingredients: [{ name: "", amount: "" }],
  steps: [{ description: "", tip: "", image: "" }],
});

const formRules = {
  name: [{ required: true, message: "请输入菜名", trigger: "blur" }],
  description: [{ required: true, message: "请输入描述", trigger: "blur" }],
  category: [{ required: true, message: "请选择分类", trigger: "change" }],
  difficulty: [{ required: true, message: "请选择难度", trigger: "change" }],
  cookTime: [{ required: true, message: "请输入制作时间", trigger: "blur" }],
  servings: [{ required: true, message: "请输入份量", trigger: "blur" }],
};

// 辅助方法
const getCategoryName = (category) => {
  const categoryMap = {
    home: "家常菜",
    specialty: "拿手菜",
    dessert: "甜品",
    soup: "汤羹",
  };
  return categoryMap[category] || category;
};

const getCategoryType = (category) => {
  const typeMap = {
    home: "success",
    specialty: "warning",
    dessert: "danger",
    soup: "info",
  };
  return typeMap[category] || "";
};

const getDifficultyText = (difficulty) => {
  const difficultyMap = {
    easy: "简单",
    medium: "中等",
    hard: "挑战",
  };
  return difficultyMap[difficulty] || difficulty;
};

const getDifficultyType = (difficulty) => {
  const typeMap = {
    easy: "success",
    medium: "warning",
    hard: "danger",
  };
  return typeMap[difficulty] || "";
};

// 图片处理方法
const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

const beforeImageUpload = async (file) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("图片大小不能超过 2MB!");
    return false;
  }

  try {
    const base64 = await convertToBase64(file);
    formData.image = base64;
    formData.images = [base64];
    ElMessage.success("图片上传成功");
  } catch (error) {
    ElMessage.error("图片转换失败");
  }

  return false; // 阻止自动上传
};

const handleImageSuccess = () => {
  // 这个方法不会被调用，因为我们阻止了自动上传
};

const beforeStepImageUpload = async (file, stepIndex) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("图片大小不能超过 2MB!");
    return false;
  }

  try {
    const base64 = await convertToBase64(file);
    formData.steps[stepIndex].image = base64;
    ElMessage.success("步骤图片上传成功");
  } catch (error) {
    ElMessage.error("图片转换失败");
  }

  return false; // 阻止自动上传
};

// 表单管理方法
const updateTags = () => {
  if (tagsInput.value.trim()) {
    formData.tags = tagsInput.value
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag);
  }
};

const removeTag = (index) => {
  formData.tags.splice(index, 1);
  tagsInput.value = formData.tags.join(", ");
};

const addIngredient = () => {
  formData.ingredients.push({ name: "", amount: "" });
};

const removeIngredient = (index) => {
  if (formData.ingredients.length > 1) {
    formData.ingredients.splice(index, 1);
  }
};

const addStep = () => {
  formData.steps.push({ description: "", tip: "", image: "" });
};

const removeStep = (index) => {
  if (formData.steps.length > 1) {
    formData.steps.splice(index, 1);
  }
};

// 数据库操作方法
/**
 * 初始化数据库数据
 * 如果数据库为空，则添加示例数据
 */
const initializeData = async () => {
  try {
    loading.value = true;
    await recipeDB.initializeSampleData();
    await loadRecipes();
  } catch (error) {
    console.error("初始化数据失败:", error);
    ElMessage.error("初始化数据失败");
  } finally {
    loading.value = false;
  }
};

/**
 * 从数据库加载菜谱数据
 */
const loadRecipes = async () => {
  try {
    loading.value = true;
    const recipes = await recipeDB.getAllRecipes();
    tableData.value = recipes;

    console.log(tableData.value, "recipes.valuerecipes.valuerecipes.value");
  } catch (error) {
    console.error("加载菜谱数据失败:", error);
    ElMessage.error("加载菜谱数据失败");
  } finally {
    loading.value = false;
  }
};

/**
 * 搜索菜谱
 */
const searchRecipes = async () => {
  try {
    loading.value = true;
    if (searchText.value.trim()) {
      const recipes = await recipeDB.searchRecipes(searchText.value.trim());
      tableData.value = recipes;
    } else {
      await loadRecipes();
    }
  } catch (error) {
    console.error("搜索菜谱失败:", error);
    ElMessage.error("搜索菜谱失败");
  } finally {
    loading.value = false;
  }
};

// Methods
const handleSearch = () => {
  currentPage.value = 1;
  searchRecipes(); // 使用数据库搜索
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

const handleAdd = () => {
  resetForm();
  editingId.value = null;
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  editingId.value = row.id;
  Object.assign(formData, { ...row });
  tagsInput.value = row.tags ? row.tags.join(", ") : "";
  dialogVisible.value = true;
};

/**
 * 删除菜谱
 * @param {Object} row - 要删除的菜谱数据
 */
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜谱 "${row.name}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    // 从数据库删除
    await recipeDB.deleteRecipe(row.id);

    // 重新加载数据
    if (searchText.value.trim()) {
      await searchRecipes();
    } else {
      await loadRecipes();
    }

    ElMessage.success("删除成功");
  } catch (error) {
    if (error === "cancel") {
      ElMessage.info("已取消删除");
    } else {
      console.error("删除菜谱失败:", error);
      ElMessage.error("删除菜谱失败");
    }
  }
};

/**
 * 保存菜谱数据（新增或编辑）
 */
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    // 更新标签
    updateTags();

    // 验证食材和步骤
    const validIngredients = formData.ingredients.filter(
      (ing) => ing.name.trim() && ing.amount.trim()
    );
    const validSteps = formData.steps.filter((step) => step.description.trim());

    if (validIngredients.length === 0) {
      ElMessage.error("请至少添加一个食材");
      return;
    }

    if (validSteps.length === 0) {
      ElMessage.error("请至少添加一个制作步骤");
      return;
    }

    saveLoading.value = true;

    // 将响应式对象转换为纯JavaScript对象，避免IndexedDB序列化错误
    const recipeData = JSON.parse(
      JSON.stringify({
        ...formData,
        ingredients: validIngredients,
        steps: validSteps,
      })
    );

    if (editingId.value) {
      // 编辑现有菜谱
      await recipeDB.updateRecipe(editingId.value, recipeData);
      ElMessage.success("更新成功");
    } else {
      // 添加新菜谱
      await recipeDB.addRecipe(recipeData);
      ElMessage.success("添加成功");
    }

    // 关闭对话框并重置表单
    dialogVisible.value = false;
    resetForm();

    // 重新加载数据
    if (searchText.value.trim()) {
      await searchRecipes();
    } else {
      await loadRecipes();
    }
  } catch (error) {
    console.error("保存菜谱失败:", error);
    ElMessage.error("保存菜谱失败");
  } finally {
    saveLoading.value = false;
  }
};

const handleDialogClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    name: "",
    description: "",
    category: "",
    image: "",
    images: [""],
    cookTime: 30,
    difficulty: "easy",
    servings: 2,
    rating: 5,
    isFavorite: false,
    spicy: "",
    tags: [],
    story: "",
    ingredients: [{ name: "", amount: "" }],
    steps: [{ description: "", tip: "", image: "" }],
  });
  tagsInput.value = "";
  editingId.value = null;
};

// 组件挂载时初始化数据
onMounted(() => {
  initializeData();
});
</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}

@keyframes vault-bounce-in {
  0% {
    transform: scale(0.8) translateY(50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(-10px);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes vault-bounce-out {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(0.8) translateY(30px);
    opacity: 0;
  }
}

/* Password Vault Modal Styles */
.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-vault-container {
  width: 95%;
  /* max-width: 1400px; */
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  padding: 20px;
}

/* Particles */
.vault-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.vault-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.vault-particle:nth-child(odd) {
  animation-delay: -2s;
}

.vault-particle:nth-child(even) {
  animation-delay: -4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
}

/* Generate random positions for particles */
.vault-particle:nth-child(1) {
  top: 10%;
  left: 10%;
}
.vault-particle:nth-child(2) {
  top: 20%;
  left: 80%;
}
.vault-particle:nth-child(3) {
  top: 30%;
  left: 30%;
}
.vault-particle:nth-child(4) {
  top: 40%;
  left: 70%;
}
.vault-particle:nth-child(5) {
  top: 50%;
  left: 20%;
}
.vault-particle:nth-child(6) {
  top: 60%;
  left: 90%;
}
.vault-particle:nth-child(7) {
  top: 70%;
  left: 40%;
}
.vault-particle:nth-child(8) {
  top: 80%;
  left: 60%;
}
.vault-particle:nth-child(9) {
  top: 15%;
  left: 50%;
}
.vault-particle:nth-child(10) {
  top: 25%;
  left: 15%;
}
.vault-particle:nth-child(11) {
  top: 35%;
  left: 85%;
}
.vault-particle:nth-child(12) {
  top: 45%;
  left: 25%;
}
.vault-particle:nth-child(13) {
  top: 55%;
  left: 75%;
}
.vault-particle:nth-child(14) {
  top: 65%;
  left: 35%;
}
.vault-particle:nth-child(15) {
  top: 75%;
  left: 95%;
}
.vault-particle:nth-child(16) {
  top: 85%;
  left: 45%;
}
.vault-particle:nth-child(17) {
  top: 5%;
  left: 65%;
}
.vault-particle:nth-child(18) {
  top: 95%;
  left: 25%;
}
.vault-particle:nth-child(19) {
  top: 12%;
  left: 88%;
}
.vault-particle:nth-child(20) {
  top: 88%;
  left: 12%;
}

/* Table content */
.table-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* height: 100%; */
  /* position: relative; */
  /* z-index: 10; */
      width: 80%;
    right: 0;
    height: calc(100% - 20px);
    position: absolute;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 20px;
}

.table-title {
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

::v-deep(.el-table tr) {
  background-color: transparent !important;
}

.title-icon {
  width: 28px;
  height: 28px;
  color: #64ffda;
}

.search-add-container {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  margin-left: 20px;
}

.search-input {
  width: 300px;
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(255, 255, 255, 0.2);
  --el-input-hover-border-color: rgba(100, 255, 218, 0.6);
  --el-input-focus-border-color: #64ffda;
  --el-input-text-color: white;
  --el-input-placeholder-color: rgba(255, 255, 255, 0.6);
}

.add-btn {
  --el-button-bg-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --el-button-border-color: transparent;
  --el-button-hover-bg-color: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* Table wrapper */
.table-wrapper {
  flex: 1;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  /* border: 1px solid rgba(255, 255, 255, 0.1); */
  overflow: auto;
}

/* Custom table styles */
:deep(.custom-table) {
  background: transparent !important;
  /* border: 1px solid rgba(255, 255, 255, 0.1) !important; */
}

:deep(.custom-table .el-table__header-wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
}

:deep(.custom-table .el-table__body-wrapper) {
  background: transparent !important;
}

:deep(.custom-table .el-table__row) {
  background: rgba(255, 255, 255, 0.02) !important;
}

:deep(.custom-table .el-table__row:hover) {
  background: rgba(100, 255, 218, 0.1) !important;
}

:deep(.custom-table .el-table__cell) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-table__inner-wrapper:before) {
  background-color: transparent !important;
}
:deep(.el-table--border) {
  background-color: transparent !important;
}
:deep(.el-table__inner-wrapper:after) {
  background-color: transparent !important;
}
:deep(.el-table--border:after) {
  background-color: transparent !important;
}
:deep(.el-table--border:before) {
  background-color: transparent !important;
}

:deep(.el-table) {
  --el-table-border-color: none !important;
}
:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: white !important;
}

.status-tag {
  font-weight: bold;
}

.action-btn {
  margin: 0 2px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Pagination */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

:deep(.custom-pagination) {
  --el-pagination-bg-color: rgba(255, 255, 255, 0.1);
  --el-pagination-text-color: white;
  --el-pagination-border-radius: 8px;
}

:deep(.custom-pagination .el-pagination__editor.el-input) {
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(255, 255, 255, 0.2);
  --el-input-text-color: white;
}

:deep(.custom-pagination .el-select) {
  --el-select-input-color: white;
  --el-select-input-focus-border-color: #64ffda;
}

:deep(.custom-pagination .btn-prev),
:deep(.custom-pagination .btn-next) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

:deep(.custom-pagination .number) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

:deep(.custom-pagination .number.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: transparent !important;
}

/* Dialog styles */
:deep(.custom-dialog) {
  --el-dialog-bg-color: rgba(15, 12, 41, 0.2);
  --el-dialog-border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

:deep(.custom-dialog .el-dialog__header) {
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  color: white;
  border-radius: 8px 8px 0 0;
  padding: 20px;
}

:deep(.custom-dialog .el-dialog__title) {
  color: white;
  font-weight: bold;
}

:deep(.custom-dialog .el-dialog__body) {
  /* background: rgba(15, 12, 41, 0.2); */
  color: white;
  padding: 30px;
}

:deep(.custom-dialog .el-dialog__footer) {
  /* background: rgba(15, 12, 41, 0.2); */
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 30px;
}

.dialog-form :deep(.el-form-item__label) {
  color: white !important;
}

.dialog-form :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.dialog-form :deep(.el-input__inner) {
  color: white !important;
}

.dialog-form :deep(.el-select__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dialog-form :deep(.el-select__placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
}

.dialog-form :deep(.el-radio__label) {
  color: white !important;
}

.dialog-form :deep(.el-date-editor.el-input) {
  --el-input-bg-color: rgba(255, 255, 255, 0.1) !important;
  --el-input-border-color: rgba(255, 255, 255, 0.2) !important;
  --el-input-text-color: white !important;
}

/* Close button */
.vault-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  /* width: 40px; */
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* 新增样式 */
.rating-display {
  display: flex;
  gap: 2px;
}

.heart-rating {
  font-size: 14px;
}

.image-uploader {
  border: 1px dashed rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #64ffda;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-uploader-icon {
  font-size: 28px;
  color: rgba(255, 255, 255, 0.6);
}

.ingredients-manager,
.steps-manager {
  width: 100%;
}

.ingredient-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
}

.step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-image-uploader {
  margin-top: 10px;
}

.step-uploaded-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.tags-display {
  margin-top: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
  .password-vault-container {
    width: 98%;
    height: 95vh;
    border-radius: 12px;
    padding: 15px;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-add-container {
    justify-content: space-between;
  }

  .search-input {
    width: 200px;
  }

  .table-title {
    font-size: 20px;
    text-align: center;
  }

  .ingredient-item {
    flex-direction: column;
    align-items: stretch;
  }

  .ingredient-item .el-input {
    margin-bottom: 10px;
    margin-right: 0 !important;
  }

  .step-item {
    flex-direction: column;
  }

  .step-number {
    margin-bottom: 10px;
    margin-right: 0;
  }
}
</style>
