<template>
  <!-- Memo Admin Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="memo-admin-overlay" @click.self="$emit('close')">
      <Transition name="memo-bounce">
        <div v-if="show" class="memo-admin-container">
          <!-- Background particles -->
          <div class="memo-particles">
            <div v-for="i in 20" :key="i" class="memo-particle"></div>
          </div>

          <!-- Close button -->
          <button @click="$emit('close')" class="memo-close-btn">
            <X class="memo-icon" />
          </button>

          <!-- Main content -->
          <div class="memo-admin-content">
            <!-- Left sidebar menu -->
            <div class="memo-sidebar">
              <div class="sidebar-header">
                <h2 class="sidebar-title">
                  <FileText class="sidebar-icon" />
                  备忘录管理
                </h2>
              </div>

              <el-menu
                v-model:default-active="activeMenu"
                class="memo-menu"
                background-color="transparent"
                text-color="rgba(255, 255, 255, 0.8)"
                active-text-color="#48dbfb"
                @select="handleMenuSelect"
              >
                <el-menu-item index="management">
                  <el-icon><FileText /></el-icon>
                  <span>备忘录管理</span>
                </el-menu-item>
                <el-menu-item index="statistics">
                  <el-icon><BarChart /></el-icon>
                  <span>数据统计</span>
                </el-menu-item>
                <el-menu-item index="settings">
                  <el-icon><Settings /></el-icon>
                  <span>系统设置</span>
                </el-menu-item>
              </el-menu>
            </div>

            <!-- Right content area -->
            <div class="memo-main-content">
              <Transition name="content-fade" mode="out-in">
                <component :is="currentComponent" :key="activeMenu" />
              </Transition>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, shallowRef } from 'vue';
import { X, FileText, BarChart, Settings } from 'lucide-vue-next';
import MemoManagement from './components/MemoManagement.vue';
import MemoStatistics from './components/MemoStatistics.vue';
import MemoSettings from './components/MemoSettings.vue';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);

// Data
const activeMenu = ref('management');

// Component mapping
const componentMap = {
  management: MemoManagement,
  statistics: MemoStatistics,
  settings: MemoSettings
};

// Computed
const currentComponent = computed(() => {
  return componentMap[activeMenu.value] || MemoManagement;
});

// Methods
const handleMenuSelect = (index) => {
  activeMenu.value = index;
};
</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.memo-bounce-enter-active {
  animation: memo-bounce-in 0.5s ease-out;
}

.memo-bounce-leave-active {
  animation: memo-bounce-out 0.3s ease-in;
}

@keyframes memo-bounce-in {
  0% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes memo-bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
}

/* Content transitions */
.content-fade-enter-active,
.content-fade-leave-active {
  transition: all 0.3s ease;
}

.content-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.content-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* Main styles */
.memo-admin-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.memo-admin-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.memo-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.memo-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(72, 219, 251, 0.3);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.memo-particle:nth-child(odd) {
  animation-delay: -2s;
  background: rgba(255, 204, 128, 0.3);
}

.memo-particle:nth-child(3n) {
  animation-delay: -4s;
  background: rgba(165, 214, 167, 0.3);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

.memo-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 30;
}

.memo-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.memo-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.memo-admin-content {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.memo-sidebar {
  width: 250px;
  background: rgba(0, 0, 0, 0.3);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 30px 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.sidebar-icon {
  width: 24px;
  height: 24px;
  color: #48dbfb;
}

.memo-menu {
  flex: 1;
  border: none;
  background: transparent;
}

.memo-menu :deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  margin: 5px 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.memo-menu :deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1);
}

.memo-menu :deep(.el-menu-item.is-active) {
  background: rgba(72, 219, 251, 0.2);
  color: #48dbfb;
}

.memo-menu :deep(.el-menu-item .el-icon) {
  margin-right: 10px;
  font-size: 16px;
}

.memo-main-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.02);
  overflow: hidden;
}

/* Responsive */
@media (max-width: 768px) {
  .memo-admin-container {
    width: 98%;
    height: 95vh;
  }

  .memo-sidebar {
    width: 200px;
  }

  .sidebar-title {
    font-size: 1.1rem;
  }
}

/* Particle positioning */
.memo-particle:nth-child(1) { top: 10%; left: 10%; animation-duration: 6s; }
.memo-particle:nth-child(2) { top: 20%; left: 80%; animation-duration: 8s; }
.memo-particle:nth-child(3) { top: 30%; left: 30%; animation-duration: 7s; }
.memo-particle:nth-child(4) { top: 40%; left: 70%; animation-duration: 9s; }
.memo-particle:nth-child(5) { top: 50%; left: 20%; animation-duration: 6s; }
.memo-particle:nth-child(6) { top: 60%; left: 90%; animation-duration: 8s; }
.memo-particle:nth-child(7) { top: 70%; left: 40%; animation-duration: 7s; }
.memo-particle:nth-child(8) { top: 80%; left: 60%; animation-duration: 9s; }
.memo-particle:nth-child(9) { top: 15%; left: 50%; animation-duration: 6s; }
.memo-particle:nth-child(10) { top: 25%; left: 15%; animation-duration: 8s; }
.memo-particle:nth-child(11) { top: 35%; left: 85%; animation-duration: 7s; }
.memo-particle:nth-child(12) { top: 45%; left: 25%; animation-duration: 9s; }
.memo-particle:nth-child(13) { top: 55%; left: 75%; animation-duration: 6s; }
.memo-particle:nth-child(14) { top: 65%; left: 35%; animation-duration: 8s; }
.memo-particle:nth-child(15) { top: 75%; left: 85%; animation-duration: 7s; }
.memo-particle:nth-child(16) { top: 85%; left: 45%; animation-duration: 9s; }
.memo-particle:nth-child(17) { top: 5%; left: 65%; animation-duration: 6s; }
.memo-particle:nth-child(18) { top: 95%; left: 25%; animation-duration: 8s; }
.memo-particle:nth-child(19) { top: 12%; left: 75%; animation-duration: 7s; }
.memo-particle:nth-child(20) { top: 88%; left: 55%; animation-duration: 9s; }
</style>