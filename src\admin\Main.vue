<template>
  <!-- Main Dashboard Modal -->
  <Transition name="modal-fade">
    <div
      v-if="show"
      class="dashboard-overlay"
      @click.self="$emit('close')"
    >
      <Transition name="vault-bounce">
        <div v-if="show" class="dashboard-container">
          <!-- Vault particles -->
          <div class="vault-particles">
            <div v-for="i in 30" :key="i" class="vault-particle" />
          </div>

          <!-- Dashboard Content -->
          <div class="dashboard-content">
            <!-- Header -->
            <div class="dashboard-header">
              <h1 class="dashboard-title">
                <!-- <Zap class="title-icon" /> -->
                管理中心
              </h1>
              <p class="dashboard-subtitle">
                Welcome to the Future of Data Management
              </p>
            </div>

            <!-- Feature Grid -->
            <div class="features-grid">
              <!-- User Management -->
              <div class="feature-module" @click="openUserManagement">
                <!-- <div class="module-icon-wrapper">
                  <Users class="module-icon" />
                  <div class="icon-glow" />
                </div> -->
                <div class="logo-icon">
                  <img src="../assets/food_logo.png" alt="" srcset="">
                </div>
                <h3 class="module-title">美食MALL</h3>
                <p class="module-description">
                  完整的用户增删改查功能，支持搜索和分页
                </p>
                <div class="module-stats">
                  <span class="stat-item">
                    <Database class="stat-icon" />
                    {{ userCount }} 用户
                  </span>
                  <span class="stat-item">
                    <Activity class="stat-icon" />
                    实时更新
                  </span>
                </div>
              </div>

              <!-- Data Analytics -->
              <div class="feature-module" @click="openAnalytics">
                <div class="module-icon-wrapper">
                  <BarChart3 class="module-icon" />
                  <div class="icon-glow" />
                </div>
                <h3 class="module-title">备忘录</h3>
                <p class="module-description">
                  可视化数据图表，深度洞察业务趋势
                </p>
                <div class="module-stats">
                  <span class="stat-item">
                    <TrendingUp class="stat-icon" />
                    +15% 增长
                  </span>
                  <span class="stat-item">
                    <Target class="stat-icon" />
                    高精度
                  </span>
                </div>
              </div>

              <!-- File Management -->
              <div class="feature-module" @click="openFileManager">
                <div class="module-icon-wrapper">
                  <FolderOpen class="module-icon" />
                  <div class="icon-glow" />
                </div>
                <h3 class="module-title">文件管理</h3>
                <p class="module-description">
                  智能文件存储，支持多格式预览和分享
                </p>
                <div class="module-stats">
                  <span class="stat-item">
                    <Cloud class="stat-icon" />
                    云存储
                  </span>
                  <span class="stat-item">
                    <Shield class="stat-icon" />
                    安全加密
                  </span>
                </div>
              </div>

              <!-- System Settings -->
              <!-- <div class="feature-module" @click="openSettings">
                <div class="module-icon-wrapper">
                  <Settings class="module-icon" />
                  <div class="icon-glow" />
                </div>
                <h3 class="module-title">系统设置</h3>
                <p class="module-description">
                  个性化配置，主题切换和权限管理
                </p>
                <div class="module-stats">
                  <span class="stat-item">
                    <Palette class="stat-icon" />
                    多主题
                  </span>
                  <span class="stat-item">
                    <Lock class="stat-icon" />
                    权限控制
                  </span>
                </div>
              </div> -->

              <!-- API Integration -->
              <!-- <div class="feature-module" @click="openApiCenter">
                <div class="module-icon-wrapper">
                  <Wifi class="module-icon" />
                  <div class="icon-glow" />
                </div>
                <h3 class="module-title">API 中心</h3>
                <p class="module-description">
                  开放接口管理，第三方服务集成
                </p>
                <div class="module-stats">
                  <span class="stat-item">
                    <Zap class="stat-icon" />
                    高性能
                  </span>
                  <span class="stat-item">
                    <GitBranch class="stat-icon" />
                    RESTful
                  </span>
                </div>
              </div> -->

              <!-- Notifications -->
              <!-- <div class="feature-module" @click="openNotifications">
                <div class="module-icon-wrapper">
                  <Bell class="module-icon" />
                  <div class="icon-glow" />
                  <div class="notification-badge">3</div>
                </div>
                <h3 class="module-title">消息中心</h3>
                <p class="module-description">
                  实时通知推送，重要信息不错过
                </p>
                <div class="module-stats">
                  <span class="stat-item">
                    <MessageSquare class="stat-icon" />
                    3 新消息
                  </span>
                  <span class="stat-item">
                    <Clock class="stat-icon" />
                    实时推送
                  </span>
                </div>
              </div> -->
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
              <button class="quick-action-btn primary" @click="openUserManagement">
                <Plus class="btn-icon" />
                快速添加用户
              </button>
              <button class="quick-action-btn secondary" @click="openAnalytics">
                <BarChart3 class="btn-icon" />
                查看报表
              </button>
              <button class="quick-action-btn tertiary" @click="exportData">
                <Download class="btn-icon" />
                导出数据
              </button>
            </div>
          </div>

          <!-- Close button -->
          <button @click="$emit('close')" class="vault-close-btn">
            <X class="vault-icon" />
          </button>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref } from "vue";
import {
  X, Zap, Users, BarChart3, FolderOpen, Settings, Wifi, Bell,
  Database, Activity, TrendingUp, Target, Cloud, Shield,
  Palette, Lock, GitBranch, MessageSquare, Clock, Plus, Download
} from "lucide-vue-next";
import { ElMessage } from "element-plus";

// Props
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["close", "openUserManagement", "openAnalytics", "openFileManager", "openSettings", "openApiCenter", "openNotifications", "openAMemo"]);

// Data
const userCount = ref(6);

// Methods
const openUserManagement = () => {
  emit('openUserManagement');
};

const openAnalytics = () => {
  emit('openAMemo');
};

const openFileManager = () => {
  ElMessage.info('文件管理功能开发中...');
};

const openSettings = () => {
  ElMessage.info('系统设置功能开发中...');
};

const openApiCenter = () => {
  ElMessage.info('API中心功能开发中...');
};

const openNotifications = () => {
  ElMessage.info('消息中心功能开发中...');
};

const exportData = () => {
  ElMessage.success('数据导出功能开发中...');
};
</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.6s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}

@keyframes vault-bounce-in {
  0% {
    transform: scale(0.7) translateY(100px);
    opacity: 0;
  }
  50% {
    transform: scale(1.02) translateY(-20px);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes vault-bounce-out {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(0.7) translateY(50px);
    opacity: 0;
  }
}

/* Dashboard Overlay */
.dashboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(15px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
  /* padding: 20px; */
}

.dashboard-container {
  width: 100%;
  /* max-width: 1600px; */
  height: 100vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  /* border-radius: 24px; */
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

/* Particles */
.vault-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.vault-particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: linear-gradient(45deg, #64ffda, #00bcd4);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-30px) translateX(20px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateY(-15px) translateX(-15px) rotate(240deg);
    opacity: 0.7;
  }
}

/* Generate random positions for particles */
.vault-particle:nth-child(1) { top: 5%; left: 10%; animation-delay: -1s; }
.vault-particle:nth-child(2) { top: 15%; left: 80%; animation-delay: -2s; }
.vault-particle:nth-child(3) { top: 25%; left: 30%; animation-delay: -3s; }
.vault-particle:nth-child(4) { top: 35%; left: 70%; animation-delay: -4s; }
.vault-particle:nth-child(5) { top: 45%; left: 20%; animation-delay: -5s; }
.vault-particle:nth-child(6) { top: 55%; left: 90%; animation-delay: -6s; }
.vault-particle:nth-child(7) { top: 65%; left: 40%; animation-delay: -7s; }
.vault-particle:nth-child(8) { top: 75%; left: 60%; animation-delay: -8s; }
.vault-particle:nth-child(9) { top: 85%; left: 15%; animation-delay: -1.5s; }
.vault-particle:nth-child(10) { top: 10%; left: 50%; animation-delay: -2.5s; }
.vault-particle:nth-child(11) { top: 20%; left: 95%; animation-delay: -3.5s; }
.vault-particle:nth-child(12) { top: 30%; left: 5%; animation-delay: -4.5s; }
.vault-particle:nth-child(13) { top: 40%; left: 85%; animation-delay: -5.5s; }
.vault-particle:nth-child(14) { top: 50%; left: 35%; animation-delay: -6.5s; }
.vault-particle:nth-child(15) { top: 60%; left: 75%; animation-delay: -7.5s; }
.vault-particle:nth-child(16) { top: 70%; left: 25%; animation-delay: -0.5s; }
.vault-particle:nth-child(17) { top: 80%; left: 65%; animation-delay: -1.8s; }
.vault-particle:nth-child(18) { top: 90%; left: 45%; animation-delay: -2.8s; }
.vault-particle:nth-child(19) { top: 8%; left: 88%; animation-delay: -3.8s; }
.vault-particle:nth-child(20) { top: 18%; left: 8%; animation-delay: -4.8s; }
.vault-particle:nth-child(21) { top: 28%; left: 78%; animation-delay: -5.8s; }
.vault-particle:nth-child(22) { top: 38%; left: 38%; animation-delay: -6.8s; }
.vault-particle:nth-child(23) { top: 48%; left: 98%; animation-delay: -7.8s; }
.vault-particle:nth-child(24) { top: 58%; left: 18%; animation-delay: -0.8s; }
.vault-particle:nth-child(25) { top: 68%; left: 58%; animation-delay: -1.2s; }
.vault-particle:nth-child(26) { top: 78%; left: 28%; animation-delay: -2.2s; }
.vault-particle:nth-child(27) { top: 88%; left: 68%; animation-delay: -3.2s; }
.vault-particle:nth-child(28) { top: 12%; left: 42%; animation-delay: -4.2s; }
.vault-particle:nth-child(29) { top: 22%; left: 92%; animation-delay: -5.2s; }
.vault-particle:nth-child(30) { top: 32%; left: 12%; animation-delay: -6.2s; }

/* Dashboard Content */
.dashboard-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px;
  position: relative;
  z-index: 10;
  overflow-y: auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
}

.dashboard-title {
  font-size: 3rem;
  font-weight: 900;
  color: white;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.title-icon {
  width: 48px;
  height: 48px;
  color: #64ffda;
  filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.6));
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.dashboard-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 1px;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
  flex: 1;
}

.feature-module {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-module .logo-icon {
  width: 50px;
  height: 50px;
  font-size: 2.5rem;
  animation: bounce 2s infinite;
  margin-bottom: 20px;
}

.feature-module::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #64ffda, #00bcd4, #64ffda);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-module:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(100, 255, 218, 0.3);
}

.feature-module:hover::before {
  opacity: 1;
}

.module-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.module-icon {
  width: 48px;
  height: 48px;
  color: #64ffda;
  filter: drop-shadow(0 0 15px rgba(100, 255, 218, 0.4));
  transition: all 0.3s ease;
}

.feature-module:hover .module-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 25px rgba(100, 255, 218, 0.8));
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(100, 255, 218, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-module:hover .icon-glow {
  opacity: 1;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.module-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
}

.module-description {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 20px;
}

.module-stats {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.05);
  padding: 5px 10px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-icon {
  width: 14px;
  height: 14px;
  color: #64ffda;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.quick-action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.quick-action-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
}

.quick-action-btn.tertiary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.quick-action-btn:hover {
  transform: translateY(-3px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 4px 15px currentColor;
}

.btn-icon {
  width: 18px;
  height: 18px;
}

/* Close button */
.vault-close-btn {
  position: absolute;
  top: 25px;
  right: 25px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  /* width: 44px; */
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  backdrop-filter: blur(10px);
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 22px;
  height: 22px;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-container {
    width: 98%;
    height: 98vh;
    border-radius: 12px;
  }

  .dashboard-content {
    padding: 20px;
  }

  .dashboard-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 10px;
  }

  .title-icon {
    width: 40px;
    height: 40px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-module {
    padding: 25px;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .quick-action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-title {
    font-size: 1.8rem;
  }

  .feature-module {
    padding: 20px;
  }

  .module-title {
    font-size: 1.3rem;
  }
}
</style>
