<template>
  <div class="memo-management">
    <!-- Header -->
    <div class="memo-header">
      <h2 class="memo-title">
        <FileText class="title-icon" />
        备忘录管理
      </h2>
      <div class="memo-actions">
        <el-input
          v-model="searchText"
          placeholder="搜索备忘录..."
          class="search-input"
          :prefix-icon="Search"
          @input="handleSearch"
        />
        <el-button
          type="primary"
          class="add-btn"
          @click="handleAdd"
          :icon="Plus"
        >
          添加备忘录
        </el-button>
      </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部" name="all" />
        <el-tab-pane label="工作" name="work" />
        <el-tab-pane label="生活" name="life" />
        <el-tab-pane label="学习" name="study" />
        <el-tab-pane label="已完成" name="completed" />
      </el-tabs>
    </div>

    <!-- Memo Cards -->
    <div class="memo-cards" v-loading="loading">
      <TransitionGroup name="memo-list" tag="div" class="cards-grid">
        <div
          v-for="memo in filteredMemos"
          :key="memo.id"
          class="memo-card"
          :class="{ 'completed': memo.isCompleted }"
          :style="{ backgroundColor: memo.color }"
        >
          <div class="card-header">
            <h3 class="card-title">{{ memo.title || '无标题' }}</h3>
            <div class="card-actions">
              <el-button
                type="text"
                size="small"
                @click="toggleComplete(memo)"
                :icon="memo.isCompleted ? CheckCircle : Circle"
                class="complete-btn"
              />
              <el-button
                type="text"
                size="small"
                @click="handleEdit(memo)"
                :icon="Edit"
                class="edit-btn"
              />
              <el-button
                type="text"
                size="small"
                @click="handleDelete(memo)"
                :icon="Trash2"
                class="delete-btn"
              />
            </div>
          </div>
          
          <div class="card-content">
            <p class="memo-preview">
              {{ memo.content.substring(0, 100) }}{{ memo.content.length > 100 ? '...' : '' }}
            </p>
          </div>
          
          <div class="card-footer">
            <div class="memo-meta">
              <el-tag v-if="memo.category" size="small" :type="getCategoryType(memo.category)">
                {{ getCategoryName(memo.category) }}
              </el-tag>
              <el-tag v-if="memo.priority" size="small" :type="getPriorityType(memo.priority)">
                {{ getPriorityName(memo.priority) }}
              </el-tag>
            </div>
            <div class="memo-date">
              {{ formatDate(memo.updatedAt) }}
            </div>
          </div>
        </div>
      </TransitionGroup>
      
      <div v-if="filteredMemos.length === 0" class="empty-state">
        <FileText class="empty-icon" />
        <p>暂无备忘录</p>
        <el-button type="primary" @click="handleAdd">创建第一个备忘录</el-button>
      </div>
    </div>

    <!-- Add/Edit Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      class="memo-dialog"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        class="memo-form"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入备忘录标题"
          />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="6"
            placeholder="请输入备忘录内容"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分类" prop="category">
              <el-select v-model="formData.category" placeholder="选择分类" style="width: 100%">
                <el-option label="工作" value="work" />
                <el-option label="生活" value="life" />
                <el-option label="学习" value="study" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="formData.priority" placeholder="选择优先级" style="width: 100%">
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="颜色">
              <div class="color-picker">
                <div
                  v-for="color in noteColors"
                  :key="color"
                  class="color-option"
                  :class="{ active: formData.color === color }"
                  :style="{ backgroundColor: color }"
                  @click="formData.color = color"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="标签">
          <el-input
            v-model="tagsInput"
            placeholder="输入标签，用逗号分隔"
            @blur="updateTags"
          />
          <div class="tags-display" v-if="formData.tags && formData.tags.length">
            <el-tag
              v-for="tag in formData.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              size="small"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            {{ editingId ? '更新' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  FileText, Search, Plus, Edit, Trash2, 
  CheckCircle, Circle 
} from 'lucide-vue-next';
import { memoDB } from '@/lib/db.js';

// Data
const loading = ref(false);
const searchText = ref('');
const activeTab = ref('all');
const memos = ref([]);

// Dialog
const dialogVisible = ref(false);
const editingId = ref(null);
const saveLoading = ref(false);

// Form
const formRef = ref();
const tagsInput = ref('');
const formData = reactive({
  title: '',
  content: '',
  category: '',
  priority: 'medium',
  color: '#ffcc80',
  tags: [],
  isCompleted: false
});

// Constants
const noteColors = [
  "#ffcc80", "#81d4fa", "#a5d6a7", "#f48fb1", "#b39ddb",
  "#ffe082", "#90caf9", "#80deea", "#ce93d8", "#ef9a9a"
];

const formRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
};

// Computed
const dialogTitle = computed(() => editingId.value ? '编辑备忘录' : '添加备忘录');

const filteredMemos = computed(() => {
  let result = memos.value;
  
  // Filter by tab
  if (activeTab.value === 'completed') {
    result = result.filter(memo => memo.isCompleted);
  } else if (activeTab.value !== 'all') {
    result = result.filter(memo => memo.category === activeTab.value);
  }
  
  // Filter by search
  if (searchText.value) {
    const keyword = searchText.value.toLowerCase();
    result = result.filter(memo =>
      memo.title.toLowerCase().includes(keyword) ||
      memo.content.toLowerCase().includes(keyword)
    );
  }
  
  return result;
});

// Methods
const loadMemos = async () => {
  try {
    loading.value = true;
    memos.value = await memoDB.getAllMemos();
  } catch (error) {
    ElMessage.error('加载备忘录失败');
    console.error('Load memos error:', error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  // Search is handled by computed property
};

const handleTabChange = () => {
  // Tab change is handled by computed property
};

const handleAdd = () => {
  resetForm();
  editingId.value = null;
  dialogVisible.value = true;
};

const handleEdit = (memo) => {
  editingId.value = memo.id;
  Object.assign(formData, {
    title: memo.title,
    content: memo.content,
    category: memo.category,
    priority: memo.priority,
    color: memo.color,
    tags: [...(memo.tags || [])],
    isCompleted: memo.isCompleted
  });
  tagsInput.value = memo.tags ? memo.tags.join(', ') : '';
  dialogVisible.value = true;
};

const handleDelete = async (memo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备忘录"${memo.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await memoDB.deleteMemo(memo.id);
    ElMessage.success('删除成功');
    await loadMemos();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
      console.error('Delete memo error:', error);
    }
  }
};

const toggleComplete = async (memo) => {
  try {
    await memoDB.updateMemo(memo.id, {
      ...memo,
      isCompleted: !memo.isCompleted
    });
    ElMessage.success(memo.isCompleted ? '标记为未完成' : '标记为已完成');
    await loadMemos();
  } catch (error) {
    ElMessage.error('更新状态失败');
    console.error('Toggle complete error:', error);
  }
};

const handleSave = async () => {
  try {
    await formRef.value.validate();
    saveLoading.value = true;
    
    const memoData = {
      title: formData.title,
      content: formData.content,
      category: formData.category,
      priority: formData.priority,
      color: formData.color,
      tags: formData.tags,
      isCompleted: formData.isCompleted
    };
    
    if (editingId.value) {
      await memoDB.updateMemo(editingId.value, memoData);
      ElMessage.success('更新成功');
    } else {
      await memoDB.addMemo(memoData);
      ElMessage.success('添加成功');
    }
    
    dialogVisible.value = false;
    await loadMemos();
  } catch (error) {
    if (error !== false) { // Not validation error
      ElMessage.error(editingId.value ? '更新失败' : '添加失败');
      console.error('Save memo error:', error);
    }
  } finally {
    saveLoading.value = false;
  }
};

const handleDialogClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(formData, {
    title: '',
    content: '',
    category: '',
    priority: 'medium',
    color: noteColors[Math.floor(Math.random() * noteColors.length)],
    tags: [],
    isCompleted: false
  });
  tagsInput.value = '';
  editingId.value = null;
  formRef.value?.clearValidate();
};

const updateTags = () => {
  if (tagsInput.value.trim()) {
    formData.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag);
  }
};

const removeTag = (tag) => {
  const index = formData.tags.indexOf(tag);
  if (index > -1) {
    formData.tags.splice(index, 1);
    tagsInput.value = formData.tags.join(', ');
  }
};

const getCategoryType = (category) => {
  const types = {
    work: 'warning',
    life: 'success',
    study: 'primary',
    other: 'info'
  };
  return types[category] || 'info';
};

const getCategoryName = (category) => {
  const names = {
    work: '工作',
    life: '生活',
    study: '学习',
    other: '其他'
  };
  return names[category] || category;
};

const getPriorityType = (priority) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  };
  return types[priority] || 'info';
};

const getPriorityName = (priority) => {
  const names = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  };
  return names[priority] || priority;
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Lifecycle
onMounted(() => {
  loadMemos();
});
</script>

<style scoped>
.memo-management {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.memo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.memo-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.title-icon {
  width: 24px;
  height: 24px;
  color: #48dbfb;
}

.memo-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-input {
  width: 250px;
}

.search-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.search-input :deep(.el-input__inner) {
  color: white;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.5);
}

.add-btn {
  background: linear-gradient(135deg, #48dbfb, #0abde3);
  border: none;
  color: white;
  font-weight: 500;
}

.add-btn:hover {
  background: linear-gradient(135deg, #0abde3, #006ba6);
}

.filter-tabs {
  margin-bottom: 20px;
}

.filter-tabs :deep(.el-tabs__header) {
  margin: 0;
}

.filter-tabs :deep(.el-tabs__nav-wrap::after) {
  background: rgba(255, 255, 255, 0.1);
}

.filter-tabs :deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.filter-tabs :deep(.el-tabs__item.is-active) {
  color: #48dbfb;
}

.filter-tabs :deep(.el-tabs__active-bar) {
  background: #48dbfb;
}

.memo-cards {
  min-height: 400px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.memo-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.memo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.memo-card.completed {
  opacity: 0.7;
}

.memo-card.completed .card-title {
  text-decoration: line-through;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  color: rgba(0, 0, 0, 0.8);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  margin-right: 10px;
}

.card-actions {
  display: flex;
  gap: 5px;
}

.card-actions .el-button {
  padding: 5px;
  min-height: auto;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.6);
}

.card-actions .el-button:hover {
  background: rgba(0, 0, 0, 0.2);
  color: rgba(0, 0, 0, 0.8);
}

.complete-btn:hover {
  color: #67c23a !important;
}

.edit-btn:hover {
  color: #409eff !important;
}

.delete-btn:hover {
  color: #f56c6c !important;
}

.card-content {
  margin-bottom: 15px;
}

.memo-preview {
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.5;
  margin: 0;
  word-break: break-word;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.memo-meta {
  display: flex;
  gap: 8px;
}

.memo-date {
  color: rgba(0, 0, 0, 0.5);
  font-size: 0.85rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 20px;
}

/* Dialog Styles */
.memo-dialog :deep(.el-dialog) {
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.memo-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 20px 15px;
}

.memo-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.memo-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.memo-form :deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.8);
}

.memo-form :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.memo-form :deep(.el-input__inner) {
  color: white;
}

.memo-form :deep(.el-textarea__inner) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.memo-form :deep(.el-select) {
  width: 100%;
}

.memo-form :deep(.el-select__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.color-picker {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: white;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.tags-display {
  margin-top: 10px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Transitions */
.memo-list-enter-active,
.memo-list-leave-active {
  transition: all 0.3s ease;
}

.memo-list-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.memo-list-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.memo-list-move {
  transition: transform 0.3s ease;
}
</style>
