<template>
  <div class="memo-settings">
    <div class="settings-header">
      <h2 class="settings-title">
        <Settings class="title-icon" />
        备忘录设置
      </h2>
    </div>

    <div class="settings-content">
      <!-- Data Management -->
      <div class="settings-section">
        <h3 class="section-title">数据管理</h3>
        <div class="settings-group">
          <div class="setting-item">
            <div class="setting-info">
              <h4>导出备忘录</h4>
              <p>将所有备忘录导出为JSON文件</p>
            </div>
            <el-button 
              type="primary" 
              @click="exportMemos"
              :loading="exportLoading"
              :icon="Download"
            >
              导出数据
            </el-button>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4>导入备忘录</h4>
              <p>从JSON文件导入备忘录数据</p>
            </div>
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="handleFileChange"
            >
              <el-button type="success" :icon="Upload">
                选择文件
              </el-button>
            </el-upload>
          </div>

          <div class="setting-item danger">
            <div class="setting-info">
              <h4>清空所有数据</h4>
              <p>删除所有备忘录数据，此操作不可恢复</p>
            </div>
            <el-button 
              type="danger" 
              @click="clearAllData"
              :loading="clearLoading"
              :icon="Trash2"
            >
              清空数据
            </el-button>
          </div>
        </div>
      </div>

      <!-- Display Settings -->
      <div class="settings-section">
        <h3 class="section-title">显示设置</h3>
        <div class="settings-group">
          <div class="setting-item">
            <div class="setting-info">
              <h4>默认颜色主题</h4>
              <p>设置新建备忘录的默认颜色</p>
            </div>
            <div class="color-selector">
              <div
                v-for="color in noteColors"
                :key="color"
                class="color-option"
                :class="{ active: settings.defaultColor === color }"
                :style="{ backgroundColor: color }"
                @click="updateSetting('defaultColor', color)"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4>默认分类</h4>
              <p>设置新建备忘录的默认分类</p>
            </div>
            <el-select 
              v-model="settings.defaultCategory" 
              @change="updateSetting('defaultCategory', $event)"
              style="width: 150px"
            >
              <el-option label="工作" value="work" />
              <el-option label="生活" value="life" />
              <el-option label="学习" value="study" />
              <el-option label="其他" value="other" />
            </el-select>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4>默认优先级</h4>
              <p>设置新建备忘录的默认优先级</p>
            </div>
            <el-select 
              v-model="settings.defaultPriority" 
              @change="updateSetting('defaultPriority', $event)"
              style="width: 150px"
            >
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- Statistics -->
      <div class="settings-section">
        <h3 class="section-title">数据统计</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总备忘录</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.pending }}</div>
            <div class="stat-label">待完成</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ formatFileSize(stats.dataSize) }}</div>
            <div class="stat-label">数据大小</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Settings, Download, Upload, Trash2 
} from 'lucide-vue-next';
import { memoDB } from '@/lib/db.js';

// Data
const exportLoading = ref(false);
const clearLoading = ref(false);
const uploadRef = ref();
const memos = ref([]);

const settings = reactive({
  defaultColor: '#ffcc80',
  defaultCategory: 'other',
  defaultPriority: 'medium'
});

const noteColors = [
  "#ffcc80", "#81d4fa", "#a5d6a7", "#f48fb1", "#b39ddb",
  "#ffe082", "#90caf9", "#80deea", "#ce93d8", "#ef9a9a"
];

// Computed
const stats = computed(() => {
  const total = memos.value.length;
  const completed = memos.value.filter(memo => memo.isCompleted).length;
  const pending = total - completed;
  const dataSize = JSON.stringify(memos.value).length;
  
  return {
    total,
    completed,
    pending,
    dataSize
  };
});

// Methods
const loadMemos = async () => {
  try {
    memos.value = await memoDB.getAllMemos();
  } catch (error) {
    console.error('Load memos error:', error);
  }
};

const loadSettings = () => {
  const savedSettings = localStorage.getItem('memoSettings');
  if (savedSettings) {
    Object.assign(settings, JSON.parse(savedSettings));
  }
};

const saveSettings = () => {
  localStorage.setItem('memoSettings', JSON.stringify(settings));
};

const updateSetting = (key, value) => {
  settings[key] = value;
  saveSettings();
  ElMessage.success('设置已保存');
};

const exportMemos = async () => {
  try {
    exportLoading.value = true;
    const data = await memoDB.getAllMemos();
    
    const exportData = {
      version: '1.0',
      exportTime: new Date().toISOString(),
      memos: data
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `memos-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    ElMessage.success('导出成功');
  } catch (error) {
    ElMessage.error('导出失败');
    console.error('Export error:', error);
  } finally {
    exportLoading.value = false;
  }
};

const handleFileChange = (file) => {
  const reader = new FileReader();
  reader.onload = async (e) => {
    try {
      const data = JSON.parse(e.target.result);
      
      if (!data.memos || !Array.isArray(data.memos)) {
        throw new Error('Invalid file format');
      }
      
      await ElMessageBox.confirm(
        `确定要导入 ${data.memos.length} 条备忘录吗？这将覆盖现有数据。`,
        '确认导入',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      
      // Clear existing data and import new data
      const db = await import('@/lib/db.js').then(m => m.default);
      await db.memos.clear();
      
      for (const memo of data.memos) {
        await memoDB.addMemo(memo);
      }
      
      await loadMemos();
      ElMessage.success('导入成功');
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('导入失败：文件格式错误');
        console.error('Import error:', error);
      }
    }
  };
  
  reader.readAsText(file.raw);
};

const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有备忘录数据吗？此操作不可恢复！',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    );
    
    clearLoading.value = true;
    const db = await import('@/lib/db.js').then(m => m.default);
    await db.memos.clear();
    
    await loadMemos();
    ElMessage.success('数据已清空');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败');
      console.error('Clear data error:', error);
    }
  } finally {
    clearLoading.value = false;
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Lifecycle
onMounted(() => {
  loadMemos();
  loadSettings();
});
</script>

<style scoped>
.memo-settings {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.settings-header {
  margin-bottom: 30px;
}

.settings-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.title-icon {
  width: 24px;
  height: 24px;
  color: #48dbfb;
}

.settings-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item.danger {
  border-color: rgba(245, 108, 108, 0.3);
  background: rgba(245, 108, 108, 0.05);
}

.setting-info h4 {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.setting-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.color-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: white;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Element Plus Overrides */
:deep(.el-select__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-input__inner) {
  color: white;
}

:deep(.el-upload) {
  display: inline-block;
}
</style>
