<template>
  <div class="memo-statistics">
    <div class="stats-header">
      <h2 class="stats-title">
        <BarChart class="title-icon" />
        备忘录统计
      </h2>
    </div>

    <div class="stats-grid">
      <!-- Overview Cards -->
      <div class="stats-card total">
        <div class="card-icon">
          <FileText />
        </div>
        <div class="card-content">
          <h3>总备忘录</h3>
          <p class="stats-number">{{ stats.total }}</p>
        </div>
      </div>

      <div class="stats-card completed">
        <div class="card-icon">
          <CheckCircle />
        </div>
        <div class="card-content">
          <h3>已完成</h3>
          <p class="stats-number">{{ stats.completed }}</p>
        </div>
      </div>

      <div class="stats-card pending">
        <div class="card-icon">
          <Clock />
        </div>
        <div class="card-content">
          <h3>待完成</h3>
          <p class="stats-number">{{ stats.pending }}</p>
        </div>
      </div>

      <div class="stats-card high-priority">
        <div class="card-icon">
          <AlertTriangle />
        </div>
        <div class="card-content">
          <h3>高优先级</h3>
          <p class="stats-number">{{ stats.highPriority }}</p>
        </div>
      </div>
    </div>

    <!-- Category Distribution -->
    <div class="category-section">
      <h3 class="section-title">分类分布</h3>
      <div class="category-chart">
        <div 
          v-for="category in categoryStats" 
          :key="category.name"
          class="category-item"
        >
          <div class="category-info">
            <span class="category-name">{{ category.label }}</span>
            <span class="category-count">{{ category.count }}</span>
          </div>
          <div class="category-bar">
            <div 
              class="category-progress"
              :style="{ 
                width: `${(category.count / stats.total) * 100}%`,
                backgroundColor: category.color 
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
      <h3 class="section-title">最近活动</h3>
      <div class="activity-list" v-loading="loading">
        <div 
          v-for="memo in recentMemos" 
          :key="memo.id"
          class="activity-item"
        >
          <div class="activity-icon" :style="{ backgroundColor: memo.color }">
            <FileText />
          </div>
          <div class="activity-content">
            <h4 class="activity-title">{{ memo.title }}</h4>
            <p class="activity-desc">{{ memo.content.substring(0, 50) }}...</p>
            <span class="activity-time">{{ formatDate(memo.updatedAt) }}</span>
          </div>
          <div class="activity-status">
            <el-tag 
              :type="memo.isCompleted ? 'success' : 'warning'"
              size="small"
            >
              {{ memo.isCompleted ? '已完成' : '进行中' }}
            </el-tag>
          </div>
        </div>
        
        <div v-if="recentMemos.length === 0" class="empty-activity">
          <p>暂无最近活动</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { 
  BarChart, FileText, CheckCircle, Clock, 
  AlertTriangle 
} from 'lucide-vue-next';
import { memoDB } from '@/lib/db.js';

// Data
const loading = ref(false);
const memos = ref([]);

// Computed
const stats = computed(() => {
  const total = memos.value.length;
  const completed = memos.value.filter(memo => memo.isCompleted).length;
  const pending = total - completed;
  const highPriority = memos.value.filter(memo => memo.priority === 'high').length;
  
  return {
    total,
    completed,
    pending,
    highPriority
  };
});

const categoryStats = computed(() => {
  const categories = {
    work: { label: '工作', count: 0, color: '#f39c12' },
    life: { label: '生活', count: 0, color: '#27ae60' },
    study: { label: '学习', count: 0, color: '#3498db' },
    other: { label: '其他', count: 0, color: '#95a5a6' }
  };
  
  memos.value.forEach(memo => {
    if (memo.category && categories[memo.category]) {
      categories[memo.category].count++;
    } else {
      categories.other.count++;
    }
  });
  
  return Object.entries(categories).map(([key, value]) => ({
    name: key,
    ...value
  })).filter(cat => cat.count > 0);
});

const recentMemos = computed(() => {
  return memos.value
    .slice()
    .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
    .slice(0, 5);
});

// Methods
const loadMemos = async () => {
  try {
    loading.value = true;
    memos.value = await memoDB.getAllMemos();
  } catch (error) {
    console.error('Load memos error:', error);
  } finally {
    loading.value = false;
  }
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const now = new Date();
  const diff = now - d;
  
  if (diff < 60000) { // 1 minute
    return '刚刚';
  } else if (diff < 3600000) { // 1 hour
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1 day
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    return d.toLocaleDateString('zh-CN');
  }
};

// Lifecycle
onMounted(() => {
  loadMemos();
});
</script>

<style scoped>
.memo-statistics {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.stats-header {
  margin-bottom: 30px;
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.title-icon {
  width: 24px;
  height: 24px;
  color: #48dbfb;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stats-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stats-card.total .card-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stats-card.completed .card-icon {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
}

.stats-card.pending .card-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stats-card.high-priority .card-icon {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
}

.card-content h3 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 0 0 5px 0;
  font-weight: 500;
}

.stats-number {
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
}

.category-section,
.activity-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.category-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.category-count {
  color: white;
  font-weight: 600;
}

.category-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.category-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.activity-content {
  flex: 1;
}

.activity-title {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.activity-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0 0 5px 0;
  line-height: 1.4;
}

.activity-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
}

.empty-activity {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.empty-activity p {
  margin: 0;
  font-size: 1rem;
}
</style>
