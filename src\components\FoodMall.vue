<template>
  <!-- Recipe Manager Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="recipe-overlay" @click.self="$emit('close')">
      <Transition name="recipe-bounce">
        <div v-if="show" class="recipe-container" 
                v-loading="loading"
                element-loading-text="加载中..."
                element-loading-background="rgba(0, 0, 0, 0.8)">
          <!-- Floating particles -->
          <div class="floating-particles">
            <div v-for="i in 30" :key="i" class="particle" :style="getParticleStyle(i)"></div>
          </div>
          
          <!-- Header -->
          <div class="recipe-header">
            <div class="header-content">
              <div class="logo-section">
                <div class="logo-icon">
                  <img src="../assets/food_logo.png" alt="" srcset="">
                </div>
                <h1 class="main-title">爱心菜谱</h1>
                <span class="subtitle">为她而做的每一道菜</span>
              </div>
              <div class="header-actions">
                <button @click="randomRecipe" class="action-btn random-btn">
                  <Shuffle class="btn-icon" />
                  随机选菜
                </button>
                <button @click="showShoppingList = !showShoppingList" class="action-btn cart-btn">
                  <ShoppingCart class="btn-icon" />
                  购物清单
                  <span v-if="shoppingList.length" class="cart-count">{{ shoppingList.length }}</span>
                </button>
                <button @click="$emit('close')" class="close-btn">
                  <X class="btn-icon" />
                </button>
              </div>
            </div>
          </div>

          <!-- Main Content -->
          <div class="recipe-main">
            <!-- Sidebar -->
            <div class="sidebar">
              <!-- Search -->
              <div class="search-section">
                <div class="search-box">
                  <Search class="search-icon" />
                  <input 
                    v-model="searchQuery" 
                    placeholder="搜索菜品、食材..." 
                    class="search-input"
                  />
                </div>
              </div>

              <!-- Categories -->
              <div class="categories-section">
                <h3 class="section-title">菜品分类</h3>
                <div class="category-list">
                  <button 
                    v-for="category in categories" 
                    :key="category.id"
                    @click="selectedCategory = category.id"
                    :class="['category-item', { active: selectedCategory === category.id }]"
                  >
                    <span class="category-icon">{{ category.icon }}</span>
                    <span class="category-name">{{ category.name }}</span>
                    <span class="category-count">{{ category.count }}</span>
                  </button>
                </div>
              </div>

              <!-- Filters -->
              <div class="filters-section">
                <h3 class="section-title">筛选条件</h3>
                <div class="filter-group">
                  <label class="filter-label">难度等级</label>
                  <select v-model="selectedDifficulty" class="filter-select">
                    <option value="">全部</option>
                    <option value="easy">简单</option>
                    <option value="medium">中等</option>
                    <option value="hard">挑战</option>
                  </select>
                </div>
                <div class="filter-group">
                  <label class="filter-label">制作时间</label>
                  <select v-model="selectedTime" class="filter-select">
                    <option value="">全部</option>
                    <option value="quick">30分钟内</option>
                    <option value="medium">30-60分钟</option>
                    <option value="long">60分钟以上</option>
                  </select>
                </div>
              </div>

              <!-- Today's Menu -->
              <div class="todays-menu-section">
                <h3 class="section-title">
                  今日菜单
                  <span class="menu-count">{{ todaysMenu.length }}</span>
                </h3>
                <div class="menu-items">
                  <div 
                    v-for="item in todaysMenu" 
                    :key="item.id"
                    class="menu-item"
                  >
                    <img :src="item.image" :alt="item.name" class="menu-item-image" />
                    <div class="menu-item-info">
                      <span class="menu-item-name">{{ item.name }}</span>
                      <button @click="removeFromMenu(item.id)" class="remove-btn">
                        <Trash2 class="remove-icon" />
                      </button>
                    </div>
                  </div>
                </div>
                <button 
                  v-if="todaysMenu.length > 0"
                  @click="generateShoppingList"
                  class="generate-list-btn"
                >
                  <List class="btn-icon" />
                  生成购物清单
                </button>
              </div>
            </div>

            <!-- Content Area -->
            <div class="content-area">
              <!-- Recipe Grid -->
              <div v-if="!selectedRecipe && !showShoppingList" class="recipe-grid">
                <div class="grid-header">
                  <h2 class="grid-title">
                    {{ selectedCategory === 'all' ? '全部菜品' : categories.find(c => c.id === selectedCategory)?.name }}
                  </h2>
                  <div class="sort-options">
                    <select v-model="sortBy" class="sort-select">
                      <option value="name">按名称排序</option>
                      <option value="rating">按评分排序</option>
                      <option value="time">按时间排序</option>
                      <option value="difficulty">按难度排序</option>
                    </select>
                  </div>
                </div>
                
                <div class="recipes-grid">
                  <div 
                    v-for="recipe in filteredRecipes" 
                    :key="recipe.id"
                    @click="selectRecipe(recipe)"
                    class="recipe-card"
                  >
                    <div class="card-image-container">
                      <img :src="recipe.image" :alt="recipe.name" class="card-image" />
                      <div class="card-overlay">
                        <button @click.stop="toggleFavorite(recipe.id)" class="favorite-btn">
                          <Heart :class="['heart-icon', { filled: recipe.isFavorite }]" />
                        </button>
                        <div class="rating-display">
                          <div class="hearts">
                            <span v-for="i in 5" :key="i" class="heart-rating">
                              {{ i <= recipe.rating ? '❤️' : '🤍' }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="card-content">
                      <h3 class="card-title">{{ recipe.name }}</h3>
                      <p class="card-description">{{ recipe.description }}</p>
                      <div class="card-meta">
                        <span class="meta-item">
                          <Clock class="meta-icon" />
                          {{ recipe.cookTime }}分钟
                        </span>
                        <span class="meta-item">
                          <ChefHat class="meta-icon" />
                          {{ getDifficultyText(recipe.difficulty) }}
                        </span>
                        <span class="meta-item spicy" v-if="recipe.spicy">
                          🌶️ {{ recipe.spicy }}
                        </span>
                      </div>
                      <div class="card-tags">
                        <span v-for="tag in recipe.tags" :key="tag" class="tag">{{ tag }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Recipe Detail -->
              <div v-if="selectedRecipe && !showShoppingList" class="recipe-detail">
                <div class="detail-header">
                  <button @click="selectedRecipe = null" class="back-btn">
                    <ArrowLeft class="btn-icon" />
                    返回
                  </button>
                  <div class="detail-actions">
                    <button @click="addToMenu(selectedRecipe)" class="action-btn add-menu-btn">
                      <Plus class="btn-icon" />
                      加入菜单
                    </button>
                    <button @click="shareRecipe(selectedRecipe)" class="action-btn share-btn">
                      <Share2 class="btn-icon" />
                      分享
                    </button>
                  </div>
                </div>

                <div class="detail-content">
                  <div class="detail-left">
                    <div class="image-gallery">
                      <div class="main-image">
                        <img :src="selectedRecipe.images[currentImageIndex]" :alt="selectedRecipe.name" />
                      </div>
                      <div class="image-thumbnails">
                        <button 
                          v-for="(image, index) in selectedRecipe.images" 
                          :key="index"
                          @click="currentImageIndex = index"
                          :class="['thumbnail', { active: currentImageIndex === index }]"
                        >
                          <img :src="image" :alt="`${selectedRecipe.name} ${index + 1}`" />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="detail-right">
                    <div class="recipe-info">
                      <h1 class="recipe-title">{{ selectedRecipe.name }}</h1>
                      <p class="recipe-story">{{ selectedRecipe.story }}</p>
                      
                      <div class="recipe-stats">
                        <div class="stat-item">
                          <Clock class="stat-icon" />
                          <span class="stat-label">制作时间</span>
                          <span class="stat-value">{{ selectedRecipe.cookTime }}分钟</span>
                        </div>
                        <div class="stat-item">
                          <ChefHat class="stat-icon" />
                          <span class="stat-label">难度等级</span>
                          <span class="stat-value">{{ getDifficultyText(selectedRecipe.difficulty) }}</span>
                        </div>
                        <div class="stat-item">
                          <Users class="stat-icon" />
                          <span class="stat-label">份量</span>
                          <span class="stat-value">{{ selectedRecipe.servings }}人份</span>
                        </div>
                      </div>

                      <div class="girlfriend-rating">
                        <h3 class="rating-title">女友心动指数</h3>
                        <div class="rating-hearts">
                          <button 
                            v-for="i in 5" 
                            :key="i"
                            @click="setRating(selectedRecipe.id, i)"
                            class="rating-heart"
                          >
                            {{ i <= selectedRecipe.rating ? '❤️' : '🤍' }}
                          </button>
                        </div>
                      </div>

                      <div class="ingredients-section">
                        <h3 class="section-title">所需食材</h3>
                        <div class="ingredients-list">
                          <div 
                            v-for="ingredient in selectedRecipe.ingredients" 
                            :key="ingredient.name"
                            class="ingredient-item"
                          >
                            <span class="ingredient-name">{{ ingredient.name }}</span>
                            <span class="ingredient-amount">{{ ingredient.amount }}</span>
                          </div>
                        </div>
                        <button @click="addIngredientsToCart(selectedRecipe)" class="add-to-cart-btn">
                          <ShoppingCart class="btn-icon" />
                          加入购物清单
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Cooking Steps -->
                <div class="cooking-steps">
                  <h3 class="section-title">制作步骤</h3>
                  <div class="steps-list">
                    <div 
                      v-for="(step, index) in selectedRecipe.steps" 
                      :key="index"
                      class="step-item"
                    >
                      <div class="step-number">{{ index + 1 }}</div>
                      <div class="step-content">
                        <div class="step-image" v-if="step.image">
                          <img :src="step.image" :alt="`步骤 ${index + 1}`" />
                        </div>
                        <div class="step-text">
                          <p class="step-description">{{ step.description }}</p>
                          <p v-if="step.tip" class="step-tip">💡 {{ step.tip }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Shopping List -->
              <div v-if="showShoppingList" class="shopping-list">
                <div class="list-header">
                  <h2 class="list-title">购物清单</h2>
                  <div class="list-actions">
                    <button @click="clearShoppingList" class="action-btn clear-btn">
                      <Trash2 class="btn-icon" />
                      清空
                    </button>
                    <button @click="shareShoppingList" class="action-btn share-btn">
                      <Share2 class="btn-icon" />
                      分享
                    </button>
                  </div>
                </div>

                <div class="list-content">
                  <div v-if="shoppingList.length === 0" class="empty-list">
                    <div class="empty-icon">🛒</div>
                    <p class="empty-text">购物清单是空的</p>
                    <p class="empty-subtitle">从菜品详情页添加食材，或者生成今日菜单的购物清单</p>
                  </div>

                  <div v-else class="shopping-categories">
                    <div 
                      v-for="category in shoppingCategories" 
                      :key="category.name"
                      class="shopping-category"
                    >
                      <h3 class="category-title">{{ category.name }}</h3>
                      <div class="category-items">
                        <div 
                          v-for="item in category.items" 
                          :key="item.id"
                          class="shopping-item"
                        >
                          <button 
                            @click="toggleShoppingItem(item.id)"
                            :class="['item-checkbox', { checked: item.completed }]"
                          >
                            <Check v-if="item.completed" class="check-icon" />
                          </button>
                          <span :class="['item-name', { completed: item.completed }]">
                            {{ item.name }}
                          </span>
                          <span class="item-amount">{{ item.amount }}</span>
                          <button @click="removeFromShoppingList(item.id)" class="remove-item-btn">
                            <X class="remove-icon" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Manual Add -->
                  <div class="manual-add">
                    <h3 class="section-title">手动添加</h3>
                    <div class="add-form">
                      <input 
                        v-model="newItemName" 
                        placeholder="食材名称" 
                        class="add-input"
                        @keyup.enter="addManualItem"
                      />
                      <input 
                        v-model="newItemAmount" 
                        placeholder="用量" 
                        class="add-input"
                        @keyup.enter="addManualItem"
                      />
                      <button @click="addManualItem" class="add-btn">
                        <Plus class="btn-icon" />
                        添加
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import {
  X, Search, Shuffle, ShoppingCart, Heart, Clock, ChefHat, Users,
  Plus, Share2, ArrowLeft, List, Trash2, Check
} from 'lucide-vue-next';
import { recipeDB } from "@/lib/db.js";

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);

// Reactive data
const searchQuery = ref('');
const selectedCategory = ref('all');
const selectedDifficulty = ref('');
const selectedTime = ref('');
const sortBy = ref('name');
const selectedRecipe = ref(null);
const currentImageIndex = ref(0);
const showShoppingList = ref(false);
const todaysMenu = ref([]);
const shoppingList = ref([]);
const newItemName = ref('');
const newItemAmount = ref('');
const loading = ref(false);

// Categories
const categories = ref([
  { id: 'all', name: '全部菜品', icon: '🍽️', count: 12 },
  { id: 'home', name: '家常菜', icon: '🏠', count: 5 },
  { id: 'specialty', name: '拿手菜', icon: '👨‍🍳', count: 3 },
  { id: 'dessert', name: '甜品', icon: '🍰', count: 2 },
  { id: 'soup', name: '汤羹', icon: '🍲', count: 2 },
  { id: 'favorite', name: '女友最爱', icon: '💕', count: 4 },
  { id: 'new', name: '尝试新菜', icon: '✨', count: 3 }
]);
// 组件挂载时初始化数据
onMounted(() => {
  initializeData();
});
// Sample recipes data
const recipes = ref([]);
// 数据库操作方法
/**
 * 初始化数据库数据
 * 如果数据库为空，则添加示例数据
 */
const initializeData = async () => {
  try {
    loading.value = true;
    await recipeDB.initializeSampleData();
    await loadRecipes();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  } finally {
    loading.value = false;
  }
};
/**
 * 从数据库加载菜谱数据
 */
const loadRecipes = async () => {
  try {
    loading.value = true;
    const recipesDatas = await recipeDB.getAllRecipes();
    recipes.value = recipesDatas;

    // console.log(recipes.value, '1111111111111')
  } catch (error) {
    console.error('加载菜谱数据失败:', error);
    ElMessage.error('加载菜谱数据失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 搜索菜谱
 */
const searchRecipes = async () => {
  try {
    loading.value = true;
    if (searchText.value.trim()) {
      const recipes = await recipeDB.searchRecipes(searchText.value.trim());
      tableData.value = recipes;
    } else {
      await loadRecipes();
    }
  } catch (error) {
    console.error('搜索菜谱失败:', error);
    ElMessage.error('搜索菜谱失败');
  } finally {
    loading.value = false;
  }
};

// Computed properties
const filteredRecipes = computed(() => {
  let filtered = recipes.value;

  // Filter by category
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(recipe => recipe.category === selectedCategory.value);
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(recipe => 
      recipe.name.toLowerCase().includes(query) ||
      recipe.description.toLowerCase().includes(query) ||
      recipe.tags.some(tag => tag.toLowerCase().includes(query)) ||
      recipe.ingredients.some(ing => ing.name.toLowerCase().includes(query))
    );
  }

  // Filter by difficulty
  if (selectedDifficulty.value) {
    filtered = filtered.filter(recipe => recipe.difficulty === selectedDifficulty.value);
  }

  // Filter by time
  if (selectedTime.value) {
    filtered = filtered.filter(recipe => {
      if (selectedTime.value === 'quick') return recipe.cookTime <= 30;
      if (selectedTime.value === 'medium') return recipe.cookTime > 30 && recipe.cookTime <= 60;
      if (selectedTime.value === 'long') return recipe.cookTime > 60;
      return true;
    });
  }

  // Sort
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'rating':
        return b.rating - a.rating;
      case 'time':
        return a.cookTime - b.cookTime;
      case 'difficulty':
        const diffOrder = { easy: 1, medium: 2, hard: 3 };
        return diffOrder[a.difficulty] - diffOrder[b.difficulty];
      default:
        return a.name.localeCompare(b.name);
    }
  });

  return filtered;
});

const shoppingCategories = computed(() => {
  const categories = {
    '蔬菜类': [],
    '肉类': [],
    '调料': [],
    '其他': []
  };

  shoppingList.value.forEach(item => {
    const category = getCategoryForIngredient(item.name);
    categories[category].push(item);
  });

  return Object.entries(categories)
    .filter(([_, items]) => items.length > 0)
    .map(([name, items]) => ({ name, items }));
});

// Methods
const getParticleStyle = (index) => {
  return {
    left: Math.random() * 100 + '%',
    animationDelay: Math.random() * 3 + 's',
    animationDuration: (Math.random() * 3 + 2) + 's'
  };
};

const getDifficultyText = (difficulty) => {
  const map = { easy: '简单', medium: '中等', hard: '挑战' };
  return map[difficulty] || difficulty;
};

const selectRecipe = (recipe) => {
  selectedRecipe.value = recipe;
  currentImageIndex.value = 0;
};

const toggleFavorite = (recipeId) => {
  const recipe = recipes.value.find(r => r.id === recipeId);
  if (recipe) {
    recipe.isFavorite = !recipe.isFavorite;
  }
};

const setRating = (recipeId, rating) => {
  const recipe = recipes.value.find(r => r.id === recipeId);
  if (recipe) {
    recipe.rating = rating;
  }
};

const addToMenu = (recipe) => {
  if (!todaysMenu.value.find(item => item.id === recipe.id)) {
    todaysMenu.value.push({
      id: recipe.id,
      name: recipe.name,
      image: recipe.image,
      cookTime: recipe.cookTime
    });
  }
};

const removeFromMenu = (recipeId) => {
  todaysMenu.value = todaysMenu.value.filter(item => item.id !== recipeId);
};

const addIngredientsToCart = (recipe) => {
  recipe.ingredients.forEach(ingredient => {
    const existingItem = shoppingList.value.find(item => item.name === ingredient.name);
    if (!existingItem) {
      shoppingList.value.push({
        id: Date.now() + Math.random(),
        name: ingredient.name,
        amount: ingredient.amount,
        completed: false,
        recipeId: recipe.id
      });
    }
  });
};

const generateShoppingList = () => {
  const allIngredients = [];
  todaysMenu.value.forEach(menuItem => {
    const recipe = recipes.value.find(r => r.id === menuItem.id);
    if (recipe) {
      recipe.ingredients.forEach(ingredient => {
        allIngredients.push({
          id: Date.now() + Math.random(),
          name: ingredient.name,
          amount: ingredient.amount,
          completed: false,
          recipeId: recipe.id
        });
      });
    }
  });

  // Merge duplicate ingredients
  const merged = {};
  allIngredients.forEach(item => {
    if (merged[item.name]) {
      // Simple merge logic - in real app, you'd want more sophisticated merging
      merged[item.name].amount += `, ${item.amount}`;
    } else {
      merged[item.name] = item;
    }
  });

  shoppingList.value = Object.values(merged);
  showShoppingList.value = true;
};

const toggleShoppingItem = (itemId) => {
  const item = shoppingList.value.find(item => item.id === itemId);
  if (item) {
    item.completed = !item.completed;
  }
};

const removeFromShoppingList = (itemId) => {
  shoppingList.value = shoppingList.value.filter(item => item.id !== itemId);
};

const clearShoppingList = () => {
  shoppingList.value = [];
};

const addManualItem = () => {
  if (newItemName.value.trim() && newItemAmount.value.trim()) {
    shoppingList.value.push({
      id: Date.now(),
      name: newItemName.value.trim(),
      amount: newItemAmount.value.trim(),
      completed: false
    });
    newItemName.value = '';
    newItemAmount.value = '';
  }
};

const getCategoryForIngredient = (name) => {
  const vegetables = ['番茄', '葱', '蒜', '姜', '洋葱', '胡萝卜'];
  const meat = ['猪肉', '牛肉', '鸡肉', '鱼', '虾', '猪梅花肉'];
  const seasoning = ['盐', '糖', '生抽', '老抽', '料酒', '蜂蜜', '可可粉'];

  if (vegetables.some(v => name.includes(v))) return '蔬菜类';
  if (meat.some(m => name.includes(m))) return '肉类';
  if (seasoning.some(s => name.includes(s))) return '调料';
  return '其他';
};

const randomRecipe = () => {
  const availableRecipes = filteredRecipes.value;
  if (availableRecipes.length > 0) {
    const randomIndex = Math.floor(Math.random() * availableRecipes.length);
    selectRecipe(availableRecipes[randomIndex]);
  }
};

const shareRecipe = (recipe) => {
  // Simulate sharing functionality
  alert(`分享菜品：${recipe.name}\n${recipe.description}`);
};

const shareShoppingList = () => {
  const listText = shoppingList.value.map(item => `${item.name}: ${item.amount}`).join('\n');
  alert(`购物清单：\n${listText}`);
};

// Update category counts
watch([recipes, selectedCategory], () => {
  categories.value.forEach(category => {
    if (category.id === 'all') {
      category.count = recipes.value.length;
    } else {
      category.count = recipes.value.filter(r => r.category === category.id).length;
    }
  });
}, { immediate: true });
</script>

<style scoped>

button:focus {
  outline: none;
}
/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.recipe-bounce-enter-active {
  animation: recipe-bounce-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.recipe-bounce-leave-active {
  animation: recipe-bounce-out 0.3s ease-in;
}

@keyframes recipe-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-100px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes recipe-bounce-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* Main container */
.recipe-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(15px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.recipe-container {
  width: 100%;
  max-width: 1600px;
  height: 95vh;
  background: linear-gradient(135deg, #2b4de3 0%, #764ba2 100%);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Floating particles */
.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float infinite ease-in-out;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Header */
.recipe-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 30px;
  z-index: 10;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  width: 50px;
  height: 50px;
  font-size: 2.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.main-title {
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-style: italic;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  /* overflow: hidden; */
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.action-btn:active {
  transform: translateY(0);
}

.random-btn {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.cart-btn {
  background: linear-gradient(45deg, #48cae4, #0077b6);
  position: relative;
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.close-btn {
  background: rgba(255, 107, 107, 0.3);
  border: none;
  border-radius: 12px;
  /* width: 40px; */
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.close-btn:hover {
  background: rgba(255, 107, 107, 0.5);
  transform: rotate(90deg);
}

.btn-icon {
  width: 18px;
  height: 18px;
}

/* Main content */
.recipe-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  z-index: 5;
}

/* Sidebar */
.sidebar {
  width: 320px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.search-section {
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-box {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.6);
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.section-title {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.category-item.active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.category-icon {
  font-size: 1.2rem;
}

.category-name {
  flex: 1;
  font-size: 0.9rem;
}

.category-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-select, .sort-select {
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus, .sort-select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.filter-select option, .sort-select option {
  background: #333;
  color: white;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

.menu-item-image {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
}

.menu-item-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-item-name {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.remove-btn {
  background: rgba(255, 107, 107, 0.3);
  border: none;
  border-radius: 6px;
  /* width: 24px; */
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: rgba(255, 107, 107, 0.5);
}

.remove-icon {
  width: 14px;
  height: 14px;
  color: white;
}

.generate-list-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: linear-gradient(45deg, #48cae4, #0077b6);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.generate-list-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 202, 228, 0.4);
}

.menu-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-left: 10px;
}

/* Content area */
.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
}

/* Recipe grid */
.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.grid-title {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.recipe-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.recipe-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.recipe-card:hover .card-image {
  transform: scale(1.1);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.7));
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.recipe-card:hover .card-overlay {
  opacity: 1;
}

.favorite-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.favorite-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.heart-icon {
  width: 20px;
  height: 20px;
  color: white;
  transition: all 0.3s ease;
}

.heart-icon.filled {
  color: #ff6b6b;
  fill: #ff6b6b;
}

.rating-display {
  display: flex;
  align-items: center;
}

.hearts {
  display: flex;
  gap: 2px;
}

.heart-rating {
  font-size: 1.2rem;
}

.card-content {
  padding: 20px;
}

.card-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.card-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.card-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

.meta-item.spicy {
  color: #ff6b6b;
  font-weight: 500;
}

.meta-icon {
  width: 14px;
  height: 14px;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Recipe detail */
.recipe-detail {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-5px);
}

.detail-actions {
  display: flex;
  gap: 15px;
}

.add-menu-btn {
  background: linear-gradient(45deg, #48cae4, #0077b6);
}

.share-btn {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-image {
  border-radius: 16px;
  overflow: hidden;
  aspect-ratio: 4/3;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
}

.thumbnail {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  width: 80px;
  height: 60px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.thumbnail:hover, .thumbnail.active {
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.recipe-title {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.recipe-story {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  line-height: 1.6;
  font-style: italic;
  margin: 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border-left: 4px solid #ff6b6b;
}

.recipe-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-align: center;
}

.stat-icon {
  width: 24px;
  height: 24px;
  color: #48cae4;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

.stat-value {
  color: white;
  font-size: 1rem;
  font-weight: 600;
}

.girlfriend-rating {
  text-align: center;
}

.rating-title {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.rating-hearts {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.rating-heart {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.rating-heart:hover {
  transform: scale(1.2);
}

.ingredients-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
}

.ingredients-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ingredient-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.ingredient-name {
  color: white;
  font-size: 0.9rem;
}

.ingredient-amount {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
}

.add-to-cart-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px;
  background: linear-gradient(45deg, #48cae4, #0077b6);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 202, 228, 0.4);
}

/* Cooking steps */
.cooking-steps {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 25px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.step-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.step-number {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.step-content {
  flex: 1;
  display: flex;
  gap: 20px;
}

.step-image {
  width: 150px;
  height: 100px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.step-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.step-text {
  flex: 1;
}

.step-description {
  color: white;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 10px 0;
}

.step-tip {
  color: #feca57;
  font-size: 0.9rem;
  font-style: italic;
  margin: 0;
  padding: 10px 15px;
  background: rgba(254, 202, 87, 0.1);
  border-radius: 8px;
  border-left: 3px solid #feca57;
}

/* Shopping list */
.shopping-list {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.list-title {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.list-actions {
  display: flex;
  gap: 15px;
}

.clear-btn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.list-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.empty-list {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: white;
}

.empty-subtitle {
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.5;
}

.shopping-categories {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.shopping-category {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.category-title {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(45deg, #48cae4, #0077b6);
  border-radius: 2px;
}

.category-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.shopping-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.shopping-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.item-checkbox {
  /* width: 24px; */
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.item-checkbox.checked {
  background: linear-gradient(45deg, #48cae4, #0077b6);
  border-color: #48cae4;
}

.check-icon {
  width: 14px;
  height: 14px;
  color: white;
}

.item-name {
  flex: 1;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.item-name.completed {
  text-decoration: line-through;
  opacity: 0.6;
}

.item-amount {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
}

.remove-item-btn {
  background: rgba(255, 107, 107, 0.3);
  border: none;
  border-radius: 6px;
  /* width: 24px; */
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.shopping-item:hover .remove-item-btn {
  opacity: 1;
}

.remove-item-btn:hover {
  background: rgba(255, 107, 107, 0.5);
  transform: scale(1.1);
}

.manual-add {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.add-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.add-input {
  flex: 1;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.add-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.add-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: linear-gradient(45deg, #48cae4, #0077b6);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 202, 228, 0.4);
}

/* Responsive design */
@media (max-width: 1200px) {
  .recipe-container {
    width: 98%;
    height: 98vh;
  }
  
  .sidebar {
    width: 280px;
  }
  
  .recipes-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
  
  .detail-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .recipe-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .recipe-main {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .recipes-grid {
    grid-template-columns: 1fr;
  }
  
  .step-content {
    flex-direction: column;
  }
  
  .step-image {
    width: 100%;
    height: 150px;
  }
  
  .add-form {
    flex-direction: column;
  }
  
  .add-input {
    width: 100%;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Loading animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s infinite;
}

/* Hover effects for interactive elements */
.recipe-card, .category-item, .shopping-item, .menu-item {
  position: relative;
  overflow: hidden;
}

.recipe-card::before, .category-item::before, .shopping-item::before, .menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.recipe-card:hover::before, .category-item:hover::before, .shopping-item:hover::before, .menu-item:hover::before {
  left: 100%;
}

/* Success animations */
@keyframes success-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.success-animation {
  animation: success-bounce 0.3s ease;
}
</style>

