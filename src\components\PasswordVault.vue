<template>
  <!-- Password Vault Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="password-vault-overlay" @click.self="$emit('close')">
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- Vault particles -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle"></div>
          </div>

          <!-- Password verification screen -->
          <div v-if="!isVaultUnlocked" class="vault-auth-screen">
            <div class="vault-auth-content">
              <div class="vault-lock-icon">
                <Lock class="lock-svg" />
              </div>
              <h2 class="vault-auth-title">密码库</h2>
              <p class="vault-auth-subtitle">请输入访问密码</p>

              <div class="vault-auth-form">
                <div class="vault-password-input-container">
                  <input
                    type="password"
                    v-model="vaultPassword"
                    placeholder="输入密码"
                    class="vault-password-input"
                    @keyup.enter="verifyVaultPassword"
                    :class="{ 'error': authError }"
                  />
                  <button @click="togglePasswordVisibility" class="vault-toggle-btn">
                    <Eye v-if="!showPassword" class="vault-eye-icon" />
                    <EyeOff v-else class="vault-eye-icon" />
                  </button>
                </div>

                <div v-if="authError" class="vault-error-message">
                  <AlertCircle class="error-icon" />
                  密码错误，请重试
                </div>

                <button @click="verifyVaultPassword" class="vault-unlock-btn">
                  <Unlock class="unlock-icon" />
                  解锁密码库
                </button>
              </div>
            </div>

            <button @click="$emit('close')" class="vault-close-btn">
              <X class="vault-icon" />
            </button>
          </div>

          <!-- Password manager screen -->
          <div v-else class="vault-manager-screen">
            <div class="vault-header">
              <div class="vault-title-container">
                <h2 class="vault-title">密码管理器</h2>
                <div class="vault-title-shadow"></div>
              </div>
              <div class="vault-actions">
                <button @click="addNewPassword" class="vault-btn add-btn">
                  <Plus class="vault-icon" />
                  新增密码
                </button>
                <button @click="lockVault" class="vault-btn lock-btn">
                  <Lock class="vault-icon" />
                  锁定
                </button>
                <button @click="$emit('close')" class="vault-btn close-btn">
                  <X class="vault-icon" />
                </button>
              </div>
            </div>

            <div class="vault-content">
              <!-- Password list -->
              <div class="password-list">
                <div class="password-search">
                  <Search class="search-icon-small" />
                  <input
                    type="text"
                    placeholder="搜索密码..."
                    v-model="passwordSearchQuery"
                    class="password-search-input"
                  />
                </div>

                <TransitionGroup name="password-list" tag="div" class="passwords-container">
                  <div
                    v-for="password in filteredPasswords"
                    :key="password.id"
                    class="password-card"
                    :class="{ 'active': activePasswordId === password.id }"
                    @click="selectPassword(password.id)"
                  >
                    <div class="password-card-header">
                      <div class="password-card-icon">
                        <Globe class="password-icon" />
                      </div>
                      <div class="password-card-info">
                        <h3 class="password-name">{{ password.name || '未命名' }}</h3>
                        <p class="password-username">{{ password.username || '无用户名' }}</p>
                      </div>
                      <div class="password-card-actions">
                        <button @click.stop="copyToClipboard(password.password)" class="password-action-btn copy-btn">
                          <Copy class="action-icon" />
                        </button>
                        <button @click.stop="deletePassword(password.id)" class="password-action-btn delete-btn">
                          <Trash2 class="action-icon" />
                        </button>
                      </div>
                    </div>
                    <div class="password-card-preview">
                      <span class="password-preview">{{ '•'.repeat(8) }}</span>
                      <span class="password-url">{{ password.url || '无网址' }}</span>
                    </div>
                  </div>
                </TransitionGroup>

                <div class="empty-vault" v-if="passwords.length === 0">
                  <Shield class="empty-icon" />
                  <p>密码库为空</p>
                  <button @click="addNewPassword" class="empty-add-btn">添加第一个密码</button>
                </div>
              </div>

              <!-- Password editor -->
              <div class="password-editor" v-if="activePassword">
                <div class="editor-header">
                  <h3>编辑密码</h3>
                  <button @click="savePassword" class="save-btn">
                    <Save class="save-icon" />
                    保存
                  </button>
                </div>

                <div class="editor-form">
                  <div class="form-group">
                    <label>名称</label>
                    <input
                      v-model="activePassword.name"
                      placeholder="例如：文心一言"
                      class="form-input"
                    />
                  </div>

                  <div class="form-group">
                    <label>账号/用户名</label>
                    <input
                      v-model="activePassword.username"
                      placeholder="例如：15966666666"
                      class="form-input"
                    />
                  </div>

                  <div class="form-group">
                    <label>密码</label>
                    <div class="password-input-group">
                      <input
                        v-model="activePassword.password"
                        :type="showEditPassword ? 'text' : 'password'"
                        placeholder="例如：90009909099"
                        class="form-input"
                      />
                      <button @click="toggleEditPasswordVisibility" class="toggle-password-btn">
                        <Eye v-if="!showEditPassword" class="eye-icon" />
                        <EyeOff v-else class="eye-icon" />
                      </button>
                      <button @click="generatePassword" class="generate-password-btn">
                        <RefreshCw class="generate-icon" />
                      </button>
                    </div>
                  </div>

                  <div class="form-group">
                    <label>网址</label>
                    <input
                      v-model="activePassword.url"
                      placeholder="例如：https://www.xxx.com"
                      class="form-input"
                    />
                  </div>

                  <div class="form-group">
                    <label>备注</label>
                    <textarea
                      v-model="activePassword.notes"
                      placeholder="添加备注信息..."
                      class="form-textarea"
                      rows="3"
                    ></textarea>
                  </div>
                </div>
              </div>

              <div class="password-editor-placeholder" v-else>
                <Key class="placeholder-icon" />
                <p>选择一个密码进行编辑</p>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import {
  Lock,
  Unlock,
  Eye,
  EyeOff,
  AlertCircle,
  Plus,
  X,
  Search,
  Globe,
  Copy,
  Trash2,
  Shield,
  Save,
  Key,
  RefreshCw
} from 'lucide-vue-next';
// 导入密码数据库操作方法
import { passwordDB } from "@/lib/db.js";

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);

// Password vault data
const isVaultUnlocked = ref(false);
const vaultPassword = ref("zhangzuying");
const showPassword = ref(false);
const authError = ref(false);
const passwords = ref([]);
const activePasswordId = ref(null);
const passwordSearchQuery = ref("");
const showEditPassword = ref(false);
const VAULT_PASSWORD = "zhangzuying"; // 固定密码

// Computed properties
const activePassword = computed(() => {
  return passwords.value.find(password => password.id === activePasswordId.value) || null;
});

const filteredPasswords = computed(() => {
  if (!passwordSearchQuery.value) return passwords.value;
  return passwords.value.filter(password =>
    password.name.toLowerCase().includes(passwordSearchQuery.value.toLowerCase()) ||
    password.username.toLowerCase().includes(passwordSearchQuery.value.toLowerCase()) ||
    password.url.toLowerCase().includes(passwordSearchQuery.value.toLowerCase())
  );
});

// Watch for show prop changes to load passwords
watch(() => props.show, (newVal) => {
  if (newVal) {
    loadPasswords();
  }
});

// Methods
const verifyVaultPassword = () => {
  if (vaultPassword.value === VAULT_PASSWORD) {
    isVaultUnlocked.value = true;
    authError.value = false;
    vaultPassword.value = "";
  } else {
    authError.value = true;
    setTimeout(() => {
      authError.value = false;
    }, 3000);
  }
};

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
  const input = document.querySelector('.vault-password-input');
  if (input) {
    input.type = showPassword.value ? 'text' : 'password';
  }
};

const lockVault = () => {
  isVaultUnlocked.value = false;
  vaultPassword.value = "";
  activePasswordId.value = null;
};

const loadPasswords = async () => {
  try {
    const savedPasswords = await passwordDB.getAllPasswords();
    if (savedPasswords && savedPasswords.length > 0) {
      passwords.value = savedPasswords;
    } else {
      // 添加示例数据
      const examplePassword = {
        id: Date.now(),
        name: '公司禅道',
        username: 'zhangzuying',
        password: 'HsoftZZY123',
        url: 'http://192.168.28.82/sa-sso/#/hsoft_login',
        notes: '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // 保存示例数据到数据库
      examplePassword.id = await passwordDB.addPassword(examplePassword);
      passwords.value = [examplePassword];
    }
  } catch (error) {
    console.error('加载密码失败:', error);
  }
};

const addNewPassword = async () => {
  const newPassword = {
    id: Date.now(),
    name: '',
    username: '',
    password: '',
    url: '',
    notes: '',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // 保存到数据库
  newPassword.id = await passwordDB.addPassword(newPassword);
  
  // 更新本地状态
  passwords.value.unshift(newPassword);
  activePasswordId.value = newPassword.id;
};

const selectPassword = async (id) => {
  activePasswordId.value = id;
  // 从数据库获取最新数据
  const password = await passwordDB.getPasswordById(id);
  if (password) {
    // 更新本地状态
    const index = passwords.value.findIndex(p => p.id === id);
    if (index !== -1) {
      passwords.value[index] = password;
    }
  }
};

const savePassword = async () => {
  if (activePassword.value) {
    try {
      // 更新数据库
      await passwordDB.updatePassword(activePassword.value.id, activePassword.value);
      
      // 更新本地状态
      const index = passwords.value.findIndex(password => password.id === activePasswordId.value);
      if (index !== -1) {
        passwords.value[index] = { ...activePassword.value };
      }
    } catch (error) {
      console.error('保存密码失败:', error);
    }
  }
};

const deletePassword = async (id) => {
  try {
    // 从数据库删除
    await passwordDB.deletePassword(id);
    
    // 更新本地状态
    const index = passwords.value.findIndex(password => password.id === id);
    if (index !== -1) {
      passwords.value.splice(index, 1);

      if (activePasswordId.value === id) {
        activePasswordId.value = passwords.value.length > 0 ? passwords.value[0].id : null;
      }
    }
  } catch (error) {
    console.error('删除密码失败:', error);
  }
};

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    // 可以添加一个提示消息
  } catch (err) {
    console.error('复制失败:', err);
  }
};

const toggleEditPasswordVisibility = () => {
  showEditPassword.value = !showEditPassword.value;
};

const generatePassword = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  if (activePassword.value) {
    activePassword.value.password = password;
  }
};
</script>

<style scoped>

button:focus {
  outline: none;
}
/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}

@keyframes vault-bounce-in {
  0% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes vault-bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
}

/* Password Vault Modal Styles */
.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-vault-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* Vault particles */
.vault-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.vault-particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(102, 126, 234, 0.8);
  border-radius: 50%;
  animation: vaultFloat 18s infinite linear;
  opacity: 0;
}

@keyframes vaultFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(80px) scale(1) rotate(360deg);
    opacity: 0;
  }
}

.vault-particles .vault-particle:nth-child(1) { left: 5%; animation-delay: 0s; }
.vault-particles .vault-particle:nth-child(2) { left: 15%; animation-delay: 1s; }
.vault-particles .vault-particle:nth-child(3) { left: 25%; animation-delay: 2s; }
.vault-particles .vault-particle:nth-child(4) { left: 35%; animation-delay: 3s; }
.vault-particles .vault-particle:nth-child(5) { left: 45%; animation-delay: 4s; }
.vault-particles .vault-particle:nth-child(6) { left: 55%; animation-delay: 5s; }
.vault-particles .vault-particle:nth-child(7) { left: 65%; animation-delay: 6s; }
.vault-particles .vault-particle:nth-child(8) { left: 75%; animation-delay: 7s; }
.vault-particles .vault-particle:nth-child(9) { left: 85%; animation-delay: 8s; }
.vault-particles .vault-particle:nth-child(10) { left: 95%; animation-delay: 9s; }
.vault-particles .vault-particle:nth-child(11) { left: 10%; animation-delay: 0.5s; }
.vault-particles .vault-particle:nth-child(12) { left: 20%; animation-delay: 1.5s; }
.vault-particles .vault-particle:nth-child(13) { left: 30%; animation-delay: 2.5s; }
.vault-particles .vault-particle:nth-child(14) { left: 40%; animation-delay: 3.5s; }
.vault-particles .vault-particle:nth-child(15) { left: 50%; animation-delay: 4.5s; }
.vault-particles .vault-particle:nth-child(16) { left: 60%; animation-delay: 5.5s; }
.vault-particles .vault-particle:nth-child(17) { left: 70%; animation-delay: 6.5s; }
.vault-particles .vault-particle:nth-child(18) { left: 80%; animation-delay: 7.5s; }
.vault-particles .vault-particle:nth-child(19) { left: 90%; animation-delay: 8.5s; }
.vault-particles .vault-particle:nth-child(20) { left: 99%; animation-delay: 9.5s; }

/* Vault authentication screen */
.vault-auth-screen {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
}

.vault-auth-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.vault-lock-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 30px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  animation: lockPulse 3s ease-in-out infinite;
}

@keyframes lockPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(102, 126, 234, 0.5);
  }
}

.lock-svg {
  width: 60px;
  height: 60px;
  color: white;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.vault-auth-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 10px;
  text-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.vault-auth-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  letter-spacing: 0.5px;
}

.vault-auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.vault-password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.vault-password-input {
  width: 100%;
  padding: 16px 50px 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  outline: none;
}

.vault-password-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(102, 126, 234, 0.6);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

.vault-password-input.error {
  border-color: rgba(255, 107, 107, 0.6);
  box-shadow: 0 0 0 4px rgba(255, 107, 107, 0.2);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.vault-password-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.vault-toggle-btn {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.vault-toggle-btn:hover {
  color: white;
  transform: scale(1.1);
}

.vault-eye-icon {
  width: 20px;
  height: 20px;
}

.vault-error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 107, 107, 0.9);
  font-size: 0.9rem;
  background: rgba(255, 107, 107, 0.1);
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-icon {
  width: 16px;
  height: 16px;
}

.vault-unlock-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.vault-unlock-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.unlock-icon {
  width: 20px;
  height: 20px;
}

.vault-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  padding: 0 12px;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* Vault manager screen */
.vault-manager-screen {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
}

.vault-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(20, 20, 35, 0.8);
}

.vault-title-container {
  position: relative;
  display: inline-block;
  perspective: 1000px;
}

.vault-title {
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(to right, #667eea, #764ba2, #a29bfe, #6c5ce7);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  letter-spacing: 2px;
  transform-style: preserve-3d;
  transform: rotateX(10deg);
  transition: transform 0.3s ease;
  margin: 0;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.vault-title-container:hover .vault-title {
  transform: rotateX(15deg) scale(1.05);
}

.vault-title-shadow {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 20px;
  background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
  transform: rotateX(90deg) translateZ(-10px);
  filter: blur(5px);
  opacity: 0.7;
}

.vault-actions {
  display: flex;
  gap: 12px;
}

.vault-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-btn {
  background: rgba(76, 175, 80, 0.2);
  color: rgba(76, 175, 80, 0.9);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.add-btn:hover {
  background: rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
}

.lock-btn {
  background: rgba(255, 193, 7, 0.2);
  color: rgba(255, 193, 7, 0.9);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.lock-btn:hover {
  background: rgba(255, 193, 7, 0.3);
  transform: translateY(-2px);
}

.close-btn {
  background: rgba(255, 107, 107, 0.2);
  color: rgba(255, 107, 107, 0.9);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.close-btn:hover {
  background: rgba(255, 107, 107, 0.3);
  transform: translateY(-2px);
}

.vault-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Password list */
.password-list {
  width: 350px;
  background: rgba(20, 20, 35, 0.5);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.password-search {
  position: relative;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-icon-small {
  position: absolute;
  left: 35px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.6);
}

.password-search-input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.password-search-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.password-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.passwords-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.password-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.password-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.password-card.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.password-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.password-card-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.password-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.password-card-info {
  flex: 1;
  min-width: 0;
}

.password-name {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.password-username {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.password-card-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.password-card:hover .password-card-actions {
  opacity: 1;
}

.password-action-btn {
  height: 32px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0 8px;
}

.copy-btn {
  background: rgba(33, 150, 243, 0.2);
  color: rgba(33, 150, 243, 0.9);
}

.copy-btn:hover {
  background: rgba(33, 150, 243, 0.3);
}

.delete-btn {
  background: rgba(255, 107, 107, 0.2);
  color: rgba(255, 107, 107, 0.9);
}

.delete-btn:hover {
  background: rgba(255, 107, 107, 0.3);
}

.action-icon {
  width: 14px;
  height: 14px;
}

.password-card-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.password-preview {
  font-family: monospace;
  letter-spacing: 2px;
}

.password-url {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.empty-vault {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  padding: 40px 20px;
}

.empty-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-add-btn {
  margin-top: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-add-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

/* Password editor */
.password-editor {
  flex: 1;
  padding: 30px;
  background: rgba(30, 30, 46, 0.7);
  overflow-y: auto;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.editor-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.save-icon {
  width: 16px;
  height: 16px;
}

.editor-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.form-input {
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.form-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.password-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-group .form-input {
  padding-right: 100px;
}

.toggle-password-btn,
.generate-password-btn {
  position: absolute;
  right: 50px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 6px;
}

.generate-password-btn {
  right: 10px;
}

.toggle-password-btn:hover,
.generate-password-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.eye-icon,
.generate-icon {
  width: 18px;
  height: 18px;
}

.form-textarea {
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-textarea:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.password-editor-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  padding: 40px;
}

.placeholder-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  opacity: 0.3;
}

/* List transitions */
.password-list-enter-active, .password-list-leave-active {
  transition: all 0.3s ease;
}

.password-list-enter-from, .password-list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.password-list-move {
  transition: transform 0.3s ease;
}
</style>