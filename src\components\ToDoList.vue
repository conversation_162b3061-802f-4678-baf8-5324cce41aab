<template>
  <!-- Password Vault Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="password-vault-overlay" @click.self="$emit('close')">
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- Vault particles background effect -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle" :style="{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${5 + Math.random() * 10}s`,
              opacity: 0.1 + Math.random() * 0.3
            }"></div>
          </div>
          
          <!-- Header section -->
          <div class="vault-header">
            <h2 class="vault-title">
              <span class="title-glow">任务管理器</span>
              <span class="title-sub">记录你每日的工作</span>
            </h2>
            <button @click="openAddTaskModal" class="add-task-btn">
              <Plus class="btn-icon" />
              <span>创建任务</span>
            </button>
            <button @click="$emit('close')" class="vault-close-btn">
              <X class="vault-icon" />
            </button>
          </div>
          
          <!-- Tabs navigation -->
          <div class="tabs-navigation">
            <button 
              v-for="tab in tabs" 
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="['tab-btn', activeTab === tab.id ? 'tab-active' : '']"
              :style="{ 
                '--tab-color': tab.color,
                '--tab-glow': tab.glow 
              }"
            >
              <tab.icon class="tab-icon" />
              <span>{{ tab.name }}</span>
              <span class="tab-badge" :style="{ backgroundColor: tab.color }">
                {{ getTabCount(tab.id) }}
              </span>
            </button>
          </div>
          
          <!-- Main content area -->
          <div class="vault-content">
            <!-- Empty state -->
            <div v-if="filteredTasks.length === 0" class="empty-state">
              <div class="empty-icon">
                <ClipboardList class="icon" />
              </div>
              <h3>暂时没有任务</h3>
              <p>去创建一个新任务吧！</p>
              <button @click="openAddTaskModal" class="empty-add-btn">
                <Plus class="btn-icon" />
                创建第一个任务
              </button>
            </div>
            
            <!-- Tasks grid -->
            <div class="tasks-grid" v-else>
              <TransitionGroup name="task-fade" tag="div" class="tasks-container">
                <div 
                  v-for="task in filteredTasks" 
                  :key="task.id"
                  class="task-card"
                  :style="{ 
                    borderLeftColor: getTaskColor(task.status)
                  }"
                >
                  <div class="task-status" :style="{ backgroundColor: getTaskColor(task.status) }">
                    {{ getStatusText(task.status) }}
                  </div>
                  
                  <h3 class="task-title">{{ task.title }}</h3>
                  <p class="task-description">{{ task.description }}</p>
                  
                  <div class="task-meta">
                    <span class="task-date">
                      <!-- <Calendar class="meta-icon" /> -->
                      {{ formatDate(task.createdAt) }}
                    </span>
                    <span v-if="task.dueDate" class="task-due">
                      <!-- <Clock class="meta-icon" /> -->
                      Due: {{ formatDate(task.dueDate) }}
                    </span>
                  </div>
                  
                  <div class="task-actions">
                    <button 
                      @click="changeTaskStatus(task.id, getNextStatus(task.status))"
                      class="action-btn status-btn"
                      :style="{ backgroundColor: getNextStatusColor(task.status) }"
                    >
                      <Check class="action-icon" />
                      {{ getNextStatusText(task.status) }}
                    </button>
                    <button @click="editTask(task)" class="action-btn edit-btn">
                      <Edit class="action-icon" />
                    </button>
                    <button @click="deleteTask(task.id)" class="action-btn delete-btn">
                      <Trash2 class="action-icon" />
                    </button>
                  </div>
                </div>
              </TransitionGroup>
            </div>
          </div>
          
          <!-- Add/Edit Task Modal -->
          <Transition name="modal-slide">
            <div v-if="showTaskModal" class="task-modal-overlay" @click.self="closeTaskModal">
              <div class="task-modal">
                <h3 class="modal-title">{{ isEditing ? '修改任务' : '创建新任务' }}</h3>
                
                <form @submit.prevent="saveTask">
                  <div class="form-group">
                    <label for="title">任务标题</label>
                    <input 
                      type="text" 
                      id="title" 
                      v-model="taskForm.title" 
                      required
                      placeholder="任务标题"
                      class="form-input"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="description">描述</label>
                    <textarea 
                      id="description" 
                      v-model="taskForm.description" 
                      placeholder="描述"
                      class="form-textarea"
                      rows="3"
                    ></textarea>
                  </div>
                  
                  <div class="form-group">
                    <label for="status">状态</label>
                    <el-select class="form-select" id="status"  v-model="taskForm.status" placeholder="请选择">
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <!-- <select 
                      id="status" 
                      v-model="taskForm.status"
                      class="form-select"
                    >
                      <option value="todo">To Do</option>
                      <option value="inProgress">In Progress</option>
                      <option value="done">Done</option>
                    </select> -->
                  </div>
                  
                  <div class="form-group">
                    <label for="dueDate">日期</label>
                    <el-date-picker
                      class="form-input"
                      v-model="taskForm.dueDate"
                      id="dueDate" 
                      type="date"
                      placeholder="Pick a day"
                    />
                    <!-- <input 
                      type="date" 
                      id="dueDate" 
                      v-model="taskForm.dueDate"
                      class="form-input"
                    > -->
                  </div>
                  
                  <div class="form-actions">
                    <button type="button" @click="closeTaskModal" class="cancel-btn">取消</button>
                    <button type="submit" class="save-btn">
                      {{ isEditing ? '更新任务' : '创建任务' }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </Transition>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { 
  X, Plus, CheckCircle, Clock, Calendar, 
  ClipboardList, Edit, Trash2, Check,
  Circle, CircleDot, CheckCircle2
} from 'lucide-vue-next';
import { todoDBOperations } from '@/lib/todoDB.js';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});
const size = ref<'default' | 'large' | 'small'>('default')
// Emits
const emit = defineEmits(['close']);

// Tabs configuration
const tabs = [
  { id: 'all', name: '全部任务', color: '#8A2BE2', glow: 'rgba(138, 43, 226, 0.4)', icon: ClipboardList },
  { id: 'todo', name: '待办', color: '#FF4757', glow: 'rgba(255, 71, 87, 0.4)', icon: Circle },
  { id: 'inProgress', name: '办理中', color: '#FFD166', glow: 'rgba(255, 209, 102, 0.4)', icon: CircleDot },
  { id: 'done', name: '办理完成', color: '#06D6A0', glow: 'rgba(6, 214, 160, 0.4)', icon: CheckCircle2 }
];
// <option value="todo">To Do</option>
//                       <option value="inProgress">In Progress</option>
//                       <option value="done">Done</option>
const options = ref([
  {
    value: 'todo',
    label: '待办'
  },
  {
    value: 'inProgress',
    label: '办理中'
  },
  {
    value: 'done',
    label: '办理完成'
  }
]);

// State
const activeTab = ref('all');
const tasks = ref([]);

// Load tasks from database on component mount
onMounted(async () => {
  try {
    const dbTasks = await todoDBOperations.getAllTasks();
    tasks.value = dbTasks;
  } catch (error) {
    console.error('Failed to load tasks:', error);
  }
});

// Task modal state
const showTaskModal = ref(false);
const isEditing = ref(false);
const currentTaskId = ref(null);
const taskForm = ref({
  title: '',
  description: '',
  status: 'todo',
  dueDate: ''
});

// Filter tasks based on active tab
const filteredTasks = computed(() => {
  if (activeTab.value === 'all') {
    return [...tasks.value].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }
  return tasks.value
    .filter(task => task.status === activeTab.value)
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
});

// Get count for each tab
const getTabCount = (tabId) => {
  if (tabId === 'all') return tasks.value.length;
  return tasks.value.filter(task => task.status === tabId).length;
};

// Get task color based on status
const getTaskColor = (status) => {
  const tab = tabs.find(t => t.id === status);
  return tab ? tab.color : tabs[0].color;
};

// Get task glow effect based on status
const getTaskGlow = (status) => {
  const tab = tabs.find(t => t.id === status);
  return tab ? tab.glow : tabs[0].glow;
};

// Get status display text
const getStatusText = (status) => {
  switch(status) {
    case 'todo': return 'To Do';
    case 'inProgress': return 'In Progress';
    case 'done': return 'Done';
    default: return 'Unknown';
  }
};

// Get next status in workflow
const getNextStatus = (status) => {
  switch(status) {
    case 'todo': return 'inProgress';
    case 'inProgress': return 'done';
    case 'done': return 'todo';
    default: return 'todo';
  }
};

// Get text for next status action
const getNextStatusText = (status) => {
  switch(status) {
    case 'todo': return '开始';
    case 'inProgress': return '完成';
    case 'done': return '重新开始';
    default: return 'Update';
  }
};

// Get color for next status button
const getNextStatusColor = (status) => {
  return getTaskColor(getNextStatus(status));
};

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

// Open add task modal
const openAddTaskModal = () => {
  isEditing.value = false;
  currentTaskId.value = null;
  taskForm.value = {
    title: '',
    description: '',
    status: 'todo',
    dueDate: ''
  };
  showTaskModal.value = true;
};

// Open edit task modal
const editTask = (task) => {
  isEditing.value = true;
  currentTaskId.value = task.id;
  taskForm.value = {
    title: task.title,
    description: task.description,
    status: task.status,
    dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : ''
  };
  showTaskModal.value = true;
};

// Close task modal
const closeTaskModal = () => {
  showTaskModal.value = false;
};

// Save task (add or update)
const saveTask = async () => {
  try {
    const taskData = {
      title: taskForm.value.title,
      description: taskForm.value.description,
      status: taskForm.value.status,
      dueDate: taskForm.value.dueDate ? new Date(taskForm.value.dueDate).toISOString() : null
    };

    if (isEditing.value && currentTaskId.value) {
      // Update existing task
      await todoDBOperations.updateTask(currentTaskId.value, taskData);
      const updatedTasks = await todoDBOperations.getAllTasks();
      tasks.value = updatedTasks;
    } else {
      // Add new task
      await todoDBOperations.addTask(taskData);
      const updatedTasks = await todoDBOperations.getAllTasks();
      tasks.value = updatedTasks;
    }
    
    closeTaskModal();
  } catch (error) {
    console.error('Failed to save task:', error);
  }
};

// Change task status
const changeTaskStatus = async (taskId, newStatus) => {
  try {
    await todoDBOperations.changeTaskStatus(taskId, newStatus);
    const updatedTasks = await todoDBOperations.getAllTasks();
    tasks.value = updatedTasks;
  } catch (error) {
    console.error('Failed to change task status:', error);
  }
};

// Delete task
const deleteTask = async (taskId) => {
  if (confirm('确定要删除此任务吗？')) {
    try {
      await todoDBOperations.deleteTask(taskId);
      const updatedTasks = await todoDBOperations.getAllTasks();
      tasks.value = updatedTasks;
    } catch (error) {
      console.error('Failed to delete task:', error);
    }
  }
};

// Animation keyframes
const style = document.createElement('style');
style.textContent = `
  @keyframes vault-bounce-in {
    0% { transform: scale(0.8); opacity: 0; }
    70% { transform: scale(1.05); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
  }
  
  @keyframes vault-bounce-out {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(0.8); opacity: 0; }
  }
  
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
  }
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
`;
document.head.appendChild(style);
</script>

<style scoped>
button {
  border-radius: 3px;
}
button:focus {
  outline: none;
}

/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}

/* Task modal transitions */
.modal-slide-enter-active, .modal-slide-leave-active {
  transition: all 0.3s ease;
}

.modal-slide-enter-from {
  opacity: 0;
  transform: translateY(-30px);
}

.modal-slide-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* Task fade transition */
.task-fade-enter-active, .task-fade-leave-active {
  transition: all 0.4s ease;
}

.task-fade-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.task-fade-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

/* Password Vault Modal Styles */
.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-vault-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* Particles effect */
.vault-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.vault-particle {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  animation: float infinite ease-in-out;
}

/* Header styles */
.vault-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 10;
}

.vault-title {
  display: flex;
  flex-direction: column;
}

.title-glow {
  color: white;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 0 15px rgba(138, 43, 226, 0.7);
}

.title-sub {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin-top: 4px;
}

.add-task-btn {
  background: #8A2BE2;
  color: white;
  border: none;
  /* border-radius: 12px; */
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
}

.add-task-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.5);
}

.btn-icon {
  width: 18px;
  height: 18px;
}

.vault-close-btn {
  position: relative;
  top: 0;
  right: 0;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 48px;
  /* width: 48px; */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* Tabs navigation */
.tabs-navigation {
  display: flex;
  gap: 12px;
  padding: 0 32px;
  margin-top: 16px;
  overflow-x: auto;
  scrollbar-width: none;
  position: relative;
  z-index: 10;
}

.tabs-navigation::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: none;
  /* border-radius: 12px; */
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-active {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 15px var(--tab-glow);
}

.tab-icon {
  width: 18px;
  height: 18px;
  color: var(--tab-color);
}

.tab-badge {
  background: var(--tab-color);
  color: white;
  border-radius: 50px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 600;
}

/* Content area */
.vault-content {
  flex: 1;
  padding: 24px 32px;
  overflow-y: auto;
  position: relative;
  z-index: 10;
}

.tasks-grid {
  width: 100%;
}

.tasks-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.task-card {
  background: rgba(255, 255, 255, 0.05);
  /* border-radius: 16px; */
  padding: 20px;
  border-left: 4px solid;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-card:hover {
  transform: translateY(-5px);
}

.task-status {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 4px 10px;
  border-radius: 50px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.task-title {
  color: white;
  font-size: 18px;
  margin-bottom: 10px;
  margin-right: 80px;
}

.task-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 50px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.meta-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.task-actions {
  display: flex;
  gap: 8px;
  /* margin-top: 16px; */
  position: absolute;
  bottom: 15px;
}

.action-btn {
  padding: 8px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
}

.status-btn {
  flex: 1;
  color: white;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.edit-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.delete-btn {
  background: rgba(255, 71, 87, 0.1);
  color: #FF4757;
}

.delete-btn:hover {
  background: rgba(255, 71, 87, 0.2);
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  padding: 40px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.icon {
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 0.4);
}

.empty-state h3 {
  font-size: 24px;
  margin-bottom: 8px;
  color: white;
}

.empty-state p {
  margin-bottom: 24px;
  max-width: 400px;
}

.empty-add-btn {
  background: #8A2BE2;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
}

.empty-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.5);
}

/* Task Modal */
.task-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
  padding: 20px;
}

.task-modal {
  background: linear-gradient(135deg, #1a1735, #2c2757);
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  padding: 24px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  color: white;
  font-size: 22px;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.form-input, .form-textarea, .form-select {
  width: 100%;
  padding: 12px 16px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 16px;
  transition: all 0.2s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: #8A2BE2;
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.2);
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 30px;
}

.cancel-btn {
  flex: 1;
  padding: 12px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.save-btn {
  flex: 1;
  padding: 12px;
  border-radius: 10px;
  border: none;
  background: #8A2BE2;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

.save-btn:hover {
  background: #9d39f2;
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .vault-header {
    padding: 16px 20px;
  }
  
  .title-glow {
    font-size: 22px;
  }
  
  .add-task-btn span {
    display: none;
  }
  
  .add-task-btn {
    padding: 12px;
  }
  
  .tabs-navigation {
    padding: 0 20px;
  }
  
  .tab-btn {
    padding: 8px 14px;
    font-size: 14px;
  }
  
  .vault-content {
    padding: 16px 20px;
  }
  
  .tasks-container {
    grid-template-columns: 1fr;
  }
}

:deep(.el-select__wrapper) {
  height: 100%;
  border-radius: 10px;
  background-color: transparent;
  box-shadow: none;
}
:deep(.el-select__wrapper.is-focused) {
  box-shadow: none;
}
:deep(.el-select__wrapper.is-hovering) {
  box-shadow: none;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-input__wrapper) {
  height: 100%;
  border-radius: 10px;
  background-color: transparent;
  box-shadow: none;
}
:deep(.el-date-editor.el-input) {
      width: 100%;
    padding: 12px 16px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: white;
    font-size: 16px;
    transition: all 0.2s ease;
    height: 50px;
}
:deep(.el-input__inner) {
  color: #fff;
}

</style>
