<template>
  <!-- Memo Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="memo-overlay" @click.self="$emit('close')">
      <Transition name="memo-bounce">
        <div v-if="show" class="memo-container">
          <div class="memo-header">
            <h2>我的备忘录</h2>
            <div class="memo-actions">
              <button @click="addNewNote" class="memo-btn add-btn">
                <PlusCircle class="memo-icon" />
              </button>
              <button @click="$emit('close')" class="memo-btn close-btn">
                <X class="memo-icon" />
              </button>
            </div>
          </div>

          <div class="memo-content">
            <!-- Notes list -->
            <TransitionGroup name="memo-list" tag="div" class="notes-list">
              <div 
                v-for="(note, index) in notes" 
                :key="note.id" 
                class="note-card"
                :class="{ 'active': activeNoteId === note.id }"
                :style="{ backgroundColor: note.color }"
                @click="selectNote(note.id)"
              >
                <div class="note-header">
                  <h3 class="note-title">{{ note.title || '无标题' }}</h3>
                  <div class="note-actions">
                    <button @click.stop="changeNoteColor(note.id)" class="note-btn color-btn">
                      <Palette class="note-btn-icon" />
                    </button>
                    <button @click.stop="deleteNote(note.id)" class="note-btn delete-btn">
                      <Trash2 class="note-btn-icon" />
                    </button>
                  </div>
                </div>
                <div class="note-preview">{{ note.content.substring(0, 80) }}{{ note.content.length > 80 ? '...' : '' }}</div>
                <div class="note-date">{{ formatDate(note.updatedAt) }}</div>
              </div>
            </TransitionGroup>

            <!-- Note editor -->
            <div class="note-editor" v-if="activeNote">
              <input 
                v-model="activeNote.title" 
                class="note-editor-title" 
                placeholder="标题" 
                @input="updateNote"
              />
              <textarea 
                v-model="activeNote.content" 
                class="note-editor-content" 
                placeholder="在这里输入备忘录内容..." 
                @input="updateNote"
              ></textarea>
            </div>
            
            <div class="empty-state" v-if="notes.length === 0">
              <FileText class="empty-icon" />
              <p>没有备忘录</p>
              <button @click="addNewNote" class="empty-btn">创建新备忘录</button>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { X, PlusCircle, Trash2, Palette, FileText } from 'lucide-vue-next';
import { memoDB } from '@/lib/db.js';
// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);

// Data
const notes = ref([]);
const activeNoteId = ref(null);
const noteColors = [
  "#ffcc80", "#81d4fa", "#a5d6a7", "#f48fb1", "#b39ddb",
  "#ffe082", "#90caf9", "#80deea", "#ce93d8", "#ef9a9a",
];

// Computed
const activeNote = computed(() => {
  return notes.value.find(note => note.id === activeNoteId.value) || null;
});

// Watch for show prop changes to load notes
watch(() => props.show, (newVal) => {
  if (newVal) {
    loadNotes();
  }
});

// Methods
const loadNotes = async () => {
  notes.value = await memoDB.getAllMemos();
  if (notes.value.length > 0 && !activeNoteId.value) {
    activeNoteId.value = notes.value[0].id;
  }
};

const saveNotes = () => {
  localStorage.setItem('memoNotes', JSON.stringify(notes.value));
};

const addNewNote = async () => {
  const newNote = {
    id: Date.now(),
    title: '',
    content: '',
    color: noteColors[Math.floor(Math.random() * noteColors.length)],
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  await memoDB.addMemo(newNote);
  await loadNotes();
  activeNoteId.value = newNote.id;
};

const selectNote = (id) => {
  activeNoteId.value = id;
};

const updateNote = async () => {
  if (activeNote.value) {
    activeNote.value.updatedAt = new Date();
    await memoDB.updateMemo(activeNote.value.id, activeNote.value);
  }
};

const deleteNote = async (id) => {
  await memoDB.deleteMemo(id);
  await loadNotes();
  
  if (activeNoteId.value === id) {
    activeNoteId.value = notes.value.length > 0 ? notes.value[0].id : null;
  }
};

const changeNoteColor = async (id) => {
  const note = notes.value.find(note => note.id === id);
  if (note) {
    const currentColorIndex = noteColors.indexOf(note.color);
    const nextColorIndex = (currentColorIndex + 1) % noteColors.length;
    note.color = noteColors[nextColorIndex];
    await memoDB.updateMemo(note.id, note);
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { 
    month: 'numeric', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.memo-bounce-enter-active {
  animation: memo-bounce-in 0.5s ease-out;
}

.memo-bounce-leave-active {
  animation: memo-bounce-out 0.3s ease-in;
}

@keyframes memo-bounce-in {
  0% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes memo-bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
}

/* Memo Modal Styles */
.memo-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.memo-container {
  width: 90%;
  max-width: 1200px;
  height: 80vh;
  background: rgba(30, 30, 46, 0.9);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.memo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(20, 20, 35, 0.8);
}

.memo-header h2 {
  font-size: 1.8rem;
  font-weight: 500;
  color: white;
  margin: 0;
  background: linear-gradient(90deg, #ff9a9e, #fad0c4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.memo-actions {
  display: flex;
  gap: 12px;
}

.memo-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  /* width: 40px; */
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.memo-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.memo-btn.add-btn:hover {
  background: rgba(72, 219, 251, 0.3);
}

.memo-btn.close-btn:hover {
  background: rgba(255, 107, 107, 0.3);
}

.memo-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.memo-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.notes-list {
  width: 300px;
  overflow-y: auto;
  padding: 20px;
  background: rgba(20, 20, 35, 0.5);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.note-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.note-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.note-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.note-card.active {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.note-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: white;
  word-break: break-word;
}

.note-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.note-card:hover .note-actions {
  opacity: 1;
}

.note-btn {
  background: rgba(0, 0, 0, 0.2);
  border: none;
  border-radius: 8px;
  /* width: 28px; */
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.note-btn:hover {
  background: rgba(0, 0, 0, 0.4);
}

.note-btn-icon {
  width: 14px;
  height: 14px;
  color: white;
}

.note-preview {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  line-height: 1.4;
  max-height: 60px;
  overflow: hidden;
  word-break: break-word;
}

.note-date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.note-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 30px;
  background: rgba(30, 30, 46, 0.7);
}

.note-editor-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
  background: transparent;
  border: none;
  outline: none;
  margin-bottom: 20px;
  padding: 0;
}

.note-editor-content {
  flex: 1;
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  padding: 0;
}

.note-editor-title::placeholder,
.note-editor-content::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  padding: 30px;
}

.empty-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
  opacity: 0.
5;
}

.empty-btn {
  margin-top: 20px;
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
</style>
