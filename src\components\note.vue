<template>
  <div class="app-container">
    <!-- 添加新便签的按钮 -->
    <button 
      class="add-note-btn"
      @click="addNewNote"
      :class="{ 'pulse-animation': showAddAnimation }"
    >
      <i class="fas fa-plus"></i> 添加新便签
    </button>

    <!-- 成功提示 -->
    <div 
      v-if="notification.show" 
      class="notification"
      :class="notification.type"
    >
      {{ notification.message }}
    </div>

    <!-- 便签组件列表 -->
    <div 
      v-for="(note, index) in notes" 
      :key="note.id" 
      class="note-wrapper"
    >
      <div 
        class="sticky-note"
        :style="{ 
          left: `${note.x}px`, 
          top: `${note.y}px`,
          zIndex: note.isDragging ? 100 : index + 1,
          backgroundColor: note.color,
          borderColor: getBorderColor(note.color)
        }"
        @mousedown="startDrag(note, $event)"
        @click="bringToFront(note)"
        :class="{ 
          'dragging': note.isDragging,
          'new-note': note.isNew 
        }"
      >
        <!-- 便签头部 - 包含标题、颜色选择和删除按钮 -->
        <div class="note-header">
          <span class="note-title">{{ note.name }}</span>
          
          <!-- 颜色选择器 -->
          <div class="color-picker" @click.stop>
            <button class="color-btn" :style="{ backgroundColor: note.color }">
              <i class="fas fa-palette"></i>
            </button>
            <div class="color-options">
              <div 
                v-for="color in colorOptions" 
                :key="color.value"
                class="color-option"
                :style="{ backgroundColor: color.value }"
                @click="changeNoteColor(note.id, color.value)"
                :title="color.name"
              ></div>
            </div>
          </div>
          
          <button 
            class="delete-btn" 
            @click.stop="deleteNote(note.id)"
            aria-label="删除便签"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <!-- 便签内容区域 -->
        <textarea 
          class="note-content"
          v-model="note.content"
          placeholder="在这里输入你的笔记内容..."
          @click.stop
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

// 颜色选项 - 10种不同背景色
const colorOptions = [
  { name: '浅黄色', value: '#fff9c4' },
  { name: '浅粉色', value: '#ffebee' },
  { name: '浅蓝色', value: '#e3f2fd' },
  { name: '浅绿色', value: '#e8f5e9' },
  { name: '浅紫色', value: '#f3e5f5' },
  { name: '浅橙色', value: '#fff3e0' },
  { name: '浅青色', value: '#e0f7fa' },
  { name: '浅红色', value: '#ffe0e0' },
  { name: '浅棕色', value: '#f5f5dc' },
  { name: '浅灰色', value: '#f5f5f5' }
];

// 生成唯一ID的函数
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 便签数据 - JSON结构
const notes = ref([]);
// 当前便签计数，用于命名
const noteCounter = ref(0);
// 当前正在拖拽的便签
const draggingNote = ref(null);
// 拖拽的偏移量
const dragOffset = ref({ x: 0, y: 0 });
// 通知提示
const notification = ref({
  show: false,
  message: '',
  type: 'success'
});
// 添加按钮动画
const showAddAnimation = ref(false);

// 计算边框颜色（基于背景色加深）
const getBorderColor = (color) => {
  // 简单的颜色转换示例，实际项目中可使用更精确的颜色处理库
  const colorMap = {
    '#fff9c4': '#fbc02d',
    '#ffebee': '#ef9a9a',
    '#e3f2fd': '#90caf9',
    '#e8f5e9': '#a5d6a7',
    '#f3e5f5': '#ce93d8',
    '#fff3e0': '#ffcc80',
    '#e0f7fa': '#80deea',
    '#ffe0e0': '#ffcdd2',
    '#f5f5dc': '#d7ccc8',
    '#f5f5f5': '#e0e0e0'
  };
  return colorMap[color] || '#ccc';
};

// 添加新便签
const addNewNote = () => {
  // 增加计数并生成名称
  noteCounter.value++;
  const noteName = `便签${noteCounter.value}`;
  
  // 默认位置：屏幕中心偏上，每次创建位置略有偏移
  const offset = (noteCounter.value - 1) * 20;
  const defaultX = (window.innerWidth - 300) / 2 + (offset % 100);
  const defaultY = (window.innerHeight - 300) / 2 - 100 + Math.floor(offset / 100) * 20;
  
  // 创建新便签（JSON结构）
  const newNote = {
    id: generateId(),
    name: noteName,
    content: '',
    color: colorOptions[0].value, // 默认第一个颜色
    x: defaultX,
    y: defaultY,
    isDragging: false,
    isNew: true // 用于新创建动画
  };
  
  notes.value.push(newNote);
  
  // 显示成功提示
  showNotification(`"${noteName}" 创建成功`);
  
  // 按钮动画
  showAddAnimation.value = true;
  setTimeout(() => {
    showAddAnimation.value = false;
  }, 500);
  
  // 3秒后移除新便签标记（结束动画）
  setTimeout(() => {
    const noteIndex = notes.value.findIndex(n => n.id === newNote.id);
    if (noteIndex !== -1) {
      notes.value[noteIndex].isNew = false;
    }
  }, 1000);
};

// 删除便签
const deleteNote = (id) => {
  const note = notes.value.find(n => n.id === id);
  if (note) {
    // 添加删除动画
    const noteEl = document.querySelector(`[data-note-id="${id}"]`);
    if (noteEl) {
      noteEl.classList.add('delete-animation');
    }
    
    // 延迟删除，等待动画完成
    setTimeout(() => {
      notes.value = notes.value.filter(note => note.id !== id);
      showNotification(`"${note.name}" 已删除`, 'info');
    }, 300);
  }
};

// 更改便签颜色
const changeNoteColor = (id, color) => {
  const note = notes.value.find(n => n.id === id);
  if (note) {
    note.color = color;
    // 添加颜色变化动画
    const noteEl = document.querySelector(`[data-note-id="${id}"]`);
    if (noteEl) {
      noteEl.classList.add('color-change');
      setTimeout(() => {
        noteEl.classList.remove('color-change');
      }, 300);
    }
  }
};

// 开始拖拽
const startDrag = (note, event) => {
  // 只有点击头部才可以拖拽
  if (!event.target.closest('.note-header')) return;
  
  draggingNote.value = note;
  note.isDragging = true;
  
  // 计算鼠标相对于便签左上角的偏移量
  dragOffset.value.x = event.clientX - note.x;
  dragOffset.value.y = event.clientY - note.y;
  
  // 阻止默认行为，避免拖拽时选中文本
  event.preventDefault();
};

// 处理拖拽移动
const handleMouseMove = (event) => {
  if (draggingNote.value) {
    // 计算新位置
    const newX = event.clientX - dragOffset.value.x;
    const newY = event.clientY - dragOffset.value.y;
    
    // 确保便签不会完全超出可视区域
    const maxX = window.innerWidth - 300;
    const maxY = window.innerHeight - 300;
    
    draggingNote.value.x = Math.max(0, Math.min(newX, maxX));
    draggingNote.value.y = Math.max(0, Math.min(newY, maxY));
  }
};

// 结束拖拽
const handleMouseUp = () => {
  if (draggingNote.value) {
    draggingNote.value.isDragging = false;
    draggingNote.value = null;
  }
};

// 将便签置于顶层
const bringToFront = (note) => {
  // 通过重新排序数组来改变z-index
  const index = notes.value.findIndex(n => n.id === note.id);
  if (index !== -1) {
    const [removed] = notes.value.splice(index, 1);
    notes.value.push(removed);
    
    // 添加前置动画
    const noteEl = document.querySelector(`[data-note-id="${note.id}"]`);
    if (noteEl) {
      noteEl.classList.add('bring-forward');
      setTimeout(() => {
        noteEl.classList.remove('bring-forward');
      }, 300);
    }
  }
};

// 显示通知
const showNotification = (message, type = 'success') => {
  notification.value = {
    show: true,
    message,
    type
  };
  
  // 3秒后自动关闭
  setTimeout(() => {
    notification.value.show = false;
  }, 3000);
};

// 监听窗口大小变化，调整便签位置避免超出屏幕
const handleResize = () => {
  notes.value.forEach(note => {
    const maxX = window.innerWidth - 300;
    const maxY = window.innerHeight - 300;
    
    note.x = Math.max(0, Math.min(note.x, maxX));
    note.y = Math.max(0, Math.min(note.y, maxY));
  });
};

// 监听鼠标移动和释放事件
onMounted(() => {
  window.addEventListener('mousemove', handleMouseMove);
  window.addEventListener('mouseup', handleMouseUp);
  window.addEventListener('resize', handleResize);
});

// 清理事件监听
onUnmounted(() => {
  window.removeEventListener('mousemove', handleMouseMove);
  window.removeEventListener('mouseup', handleMouseUp);
  window.removeEventListener('resize', handleResize);
});

// 监听便签变化，可用于持久化存储
watch(notes, (newNotes) => {
  console.log('便签数据变化:', JSON.stringify(newNotes, null, 2));
  // 实际应用中可以在这里保存到localStorage或服务器
}, { deep: true });
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f0f2f5;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.4) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(255,255,255,0.4) 0%, transparent 20%);
  padding: 20px;
  transition: background-color 0.5s ease;
}

.add-note-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(66, 185, 131, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

.add-note-btn:hover {
  transform: scale(1.1);
  background-color: #359e75;
  box-shadow: 0 6px 16px rgba(66, 185, 131, 0.5);
}

.add-note-btn i {
  margin-right: 0;
}

.pulse-animation {
  animation: pulse 0.5s ease;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@media (min-width: 768px) {
  .add-note-btn {
    width: auto;
    height: 60px;
    padding: 0 20px;
    border-radius: 30px;
    font-size: 16px;
  }
  
  .add-note-btn i {
    margin-right: 8px;
  }
}

.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 30px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  animation: slideDown 0.3s ease forwards, fadeOut 0.3s ease 2.7s forwards;
}

.notification.success {
  background-color: #4caf50;
}

.notification.info {
  background-color: #2196f3;
}

@keyframes slideDown {
  from { transform: translate(-50%, -100px); opacity: 0; }
  to { transform: translate(-50%, 0); opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.sticky-note {
  position: fixed;
  width: 300px;
  height: 300px;
  border-radius: 8px;
  border: 1px solid;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: default;
}

.sticky-note.new-note {
  animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes popIn {
  0% { transform: scale(0.8); opacity: 0; }
  70% { transform: scale(1.05); }
  100% { transform: scale(1); opacity: 1; }
}

.sticky-note.color-change {
  animation: colorShift 0.3s ease;
}

@keyframes colorShift {
  0% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
  100% { transform: rotate(0); }
}

.sticky-note.bring-forward {
  animation: liftUp 0.3s ease;
}

@keyframes liftUp {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}

.sticky-note.delete-animation {
  animation: fadeAway 0.3s ease forwards;
}

@keyframes fadeAway {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0; }
}

.sticky-note:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.sticky-note.dragging {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transform: scale(1.02) rotate(1deg);
  transition: none;
}

.note-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  transition: background-color 0.3s ease;
}

.note-title {
  font-weight: bold;
  color: #333;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.color-picker {
  position: relative;
  margin: 0 8px;
}

.color-btn {
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: all 0.2s ease;
}

.color-btn:hover {
  transform: rotate(30deg);
  color: #222;
}

.color-options {
  position: absolute;
  top: 30px;
  right: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  padding: 8px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
  width: 160px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.color-picker:hover .color-options {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 1px solid #ddd;
}

.color-option:hover {
  transform: scale(1.2);
}

.delete-btn {
  background: none;
  border: none;
  color: #795548;
  cursor: pointer;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: #d32f2f;
  transform: rotate(90deg);
}

.note-content {
  width: 100%;
  height: calc(100% - 40px);
  border: none;
  background-color: transparent;
  padding: 12px;
  resize: none;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  transition: all 0.3s ease;
}

.note-content:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.1);
}

.note-content::placeholder {
  color: #888;
  font-style: italic;
  opacity: 0.7;
}
</style>
