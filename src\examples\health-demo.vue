<template>
  <div class="health-demo">
    <h1>健康数据管理演示</h1>
    
    <div class="demo-controls">
      <button @click="showHealthModal = true" class="demo-btn primary">
        打开健康记录
      </button>
      <button @click="addSampleData" class="demo-btn secondary">
        添加示例数据
      </button>
      <button @click="clearAllData" class="demo-btn danger">
        清空所有数据
      </button>
    </div>

    <div class="demo-stats" v-if="stats">
      <div class="stat-card">
        <h3>总记录数</h3>
        <p>{{ recordCount }}</p>
      </div>
      <div class="stat-card">
        <h3>当前体重</h3>
        <p>{{ stats.current }}斤</p>
      </div>
      <div class="stat-card">
        <h3>减重成果</h3>
        <p>-{{ stats.loss.toFixed(1) }}斤</p>
      </div>
      <div class="stat-card">
        <h3>减重比例</h3>
        <p>{{ stats.lossPercentage }}%</p>
      </div>
    </div>

    <div class="demo-recent-records" v-if="recentRecords.length > 0">
      <h2>最近记录</h2>
      <div class="records-list">
        <div 
          v-for="record in recentRecords" 
          :key="record.id"
          class="record-item"
        >
          <div class="record-date">{{ record.date }} ({{ record.day }})</div>
          <div class="record-weight" v-if="record.weight">体重: {{ record.weight }}斤</div>
          <div class="record-exercise" v-if="record.exercise">运动: {{ record.exercise }}</div>
          <div class="record-meals">
            <span v-for="meal in record.meals" :key="meal.type" class="meal-tag">
              {{ meal.type }}: {{ meal.food || '未记录' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 健康模态框 -->
    <HealthModal 
      :show="showHealthModal" 
      @close="showHealthModal = false"
      @data-updated="refreshData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import HealthModal from '../components/HealthModal.vue'
import { healthDBOperations } from '../lib/healthDB.js'

// 响应式数据
const showHealthModal = ref(false)
const recordCount = ref(0)
const stats = ref(null)
const recentRecords = ref([])

// 刷新数据
const refreshData = async () => {
  try {
    // 获取记录总数
    const allRecords = await healthDBOperations.getAllHealthRecords()
    recordCount.value = allRecords.length
    
    // 获取最近5条记录
    recentRecords.value = allRecords.slice(0, 5)
    
    // 获取体重统计
    stats.value = await healthDBOperations.getWeightStats()
    
    console.log('数据刷新完成')
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

// 添加示例数据
const addSampleData = async () => {
  try {
    const sampleData = [
      {
        date: '8.10',
        day: '六',
        meals: [
          { type: '早餐', food: '燕麦粥、香蕉' },
          { type: '午饭', food: '鸡胸肉沙拉' },
          { type: '晚上', food: '蒸蛋羹、青菜' }
        ],
        exercise: '慢跑30分钟',
        weight: '142.5',
        notes: '今天状态很好，坚持运动'
      },
      {
        date: '8.11',
        day: '日',
        meals: [
          { type: '早餐', food: '全麦面包、牛奶' },
          { type: '午饭', food: '蔬菜汤、糙米饭' },
          { type: '晚上', food: '水果沙拉' }
        ],
        exercise: '瑜伽45分钟',
        weight: '142.0',
        notes: '体重持续下降，很开心'
      }
    ]
    
    await healthDBOperations.bulkImportHealthRecords(sampleData)
    await refreshData()
    alert('示例数据添加成功！')
  } catch (error) {
    console.error('添加示例数据失败:', error)
    alert('添加失败，请重试')
  }
}

// 清空所有数据
const clearAllData = async () => {
  if (confirm('确定要清空所有健康数据吗？此操作不可恢复！')) {
    try {
      await healthDBOperations.clearAllHealthRecords()
      await refreshData()
      alert('数据清空成功！')
    } catch (error) {
      console.error('清空数据失败:', error)
      alert('清空失败，请重试')
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.health-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.health-demo h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.demo-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
}

.demo-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-btn.primary {
  background: #4caf50;
  color: white;
}

.demo-btn.secondary {
  background: #2196f3;
  color: white;
}

.demo-btn.danger {
  background: #f44336;
  color: white;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.demo-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.stat-card p {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.demo-recent-records h2 {
  color: #333;
  margin-bottom: 20px;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.record-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.record-date {
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.record-weight,
.record-exercise {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.record-meals {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.meal-tag {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}
</style>
