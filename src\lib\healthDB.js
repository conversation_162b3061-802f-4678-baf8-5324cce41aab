import Dexie from 'dexie';

/**
 * 健康数据库类
 * 专门用于管理健康数据(体重、饮食、运动等)的存储
 */
class HealthDB extends Dexie {
  constructor() {
    super('HealthDB');

    // 定义数据库版本和表结构
    this.version(1).stores({
      healthRecords: '++id, date, day, meals, exercise, weight, notes, createdAt, updatedAt'
    });

    this.healthRecords = this.table('healthRecords');
  }
}

// 创建数据库实例
const healthDB = new HealthDB();

/**
 * 健康数据库操作方法
 */
export const healthDBOperations = {
  /**
   * 添加健康记录
   * @param {Object} record - 健康记录对象
   * @param {string} record.date - 日期 (如: '6.5')
   * @param {string} record.day - 星期 (如: '四')
   * @param {Array} record.meals - 餐食数组 [{ type: '早餐', food: '...' }, ...]
   * @param {string} [record.exercise] - 运动记录
   * @param {string} [record.weight] - 体重
   * @param {string} [record.notes] - 备注
   */
  async addHealthRecord(record) {
    try {
      const cleanRecord = {
        date: record.date,
        day: record.day,
        meals: record.meals || [
          { type: '早餐', food: '' },
          { type: '午饭', food: '' },
          { type: '晚上', food: '' }
        ],
        exercise: record.exercise || '',
        weight: record.weight || '',
        notes: record.notes || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await healthDB.healthRecords.add(cleanRecord);
      console.log('健康记录已保存:', cleanRecord);
      return result;
    } catch (error) {
      console.error('添加健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 更新健康记录
   * @param {number} id - 记录ID
   * @param {Object} updates - 更新内容
   */
  async updateHealthRecord(id, updates) {
    try {
      const cleanUpdates = {
        ...updates,
        updatedAt: new Date()
      };
      
      const result = await healthDB.healthRecords.update(id, cleanUpdates);
      console.log('健康记录已更新:', { id, updates: cleanUpdates });
      return result;
    } catch (error) {
      console.error('更新健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 删除健康记录
   * @param {number} id - 记录ID
   */
  async deleteHealthRecord(id) {
    try {
      const result = await healthDB.healthRecords.delete(id);
      console.log('健康记录已删除:', id);
      return result;
    } catch (error) {
      console.error('删除健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有健康记录
   * @returns {Promise<Array>} 健康记录数组
   */
  async getAllHealthRecords() {
    try {
      const records = await healthDB.healthRecords
        .orderBy('date')
        .reverse()
        .toArray();
      
      console.log('获取所有健康记录:', records);
      return records;
    } catch (error) {
      console.error('获取健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 根据日期获取健康记录
   * @param {string} date - 日期
   * @returns {Promise<Object|null>} 健康记录或null
   */
  async getHealthRecordByDate(date) {
    try {
      const record = await healthDB.healthRecords
        .where('date')
        .equals(date)
        .first();

      return record;
    } catch (error) {
      console.error('根据日期获取健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 批量导入健康数据
   * @param {Array} records - 健康记录数组
   */
  async bulkImportHealthRecords(records) {
    try {
      const cleanRecords = records.map(record => ({
        date: record.date,
        day: record.day,
        meals: record.meals || [
          { type: '早餐', food: '' },
          { type: '午饭', food: '' },
          { type: '晚上', food: '' }
        ],
        exercise: record.exercise || '',
        weight: record.weight || '',
        notes: record.notes || '',
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      const result = await healthDB.healthRecords.bulkAdd(cleanRecords);
      console.log('批量导入健康记录成功:', cleanRecords.length, '条记录');
      return result;
    } catch (error) {
      console.error('批量导入健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 清空所有健康记录
   */
  async clearAllHealthRecords() {
    try {
      const result = await healthDB.healthRecords.clear();
      console.log('所有健康记录已清空');
      return result;
    } catch (error) {
      console.error('清空健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 获取体重统计数据
   * @returns {Promise<Object>} 体重统计信息
   */
  async getWeightStats() {
    try {
      const records = await healthDB.healthRecords
        .filter(record => record.weight && record.weight !== '')
        .sortBy('date');

      if (records.length === 0) {
        return {
          current: 0,
          start: 0,
          min: 0,
          max: 0,
          loss: 0,
          lossPercentage: '0.0'
        };
      }

      const weights = records.map(r => parseFloat(r.weight)).filter(w => !isNaN(w));
      const minWeight = Math.min(...weights);
      const maxWeight = Math.max(...weights);
      const currentWeight = weights[weights.length - 1];
      const startWeight = weights[0];
      const weightLoss = startWeight - currentWeight;

      return {
        current: currentWeight,
        start: startWeight,
        min: minWeight,
        max: maxWeight,
        loss: weightLoss,
        lossPercentage: ((weightLoss / startWeight) * 100).toFixed(1)
      };
    } catch (error) {
      console.error('获取体重统计失败:', error);
      throw error;
    }
  }
};

// 默认导出数据库实例
export default healthDB;
