import <PERSON>ie from 'dexie';

class NoteDB extends <PERSON>ie {
  constructor() {
    super('MemoNotesDB');
    
    this.version(1).stores({
      notes: '++id, content, color, x, y, zIndex, createdAt, updatedAt'
    });

    this.notes = this.table('notes');
  }
}

const db = new NoteDB();

export const noteDB = {
  async addNote(note) {
    const cleanNote = JSON.parse(JSON.stringify({
      ...note,
      createdAt: new Date(),
      updatedAt: new Date()
    }));
    return await db.notes.add(cleanNote);
  },

  async updateNote(id, updates) {
    const cleanUpdates = JSON.parse(JSON.stringify({
      ...updates,
      updatedAt: new Date()
    }));
    return await db.notes.update(id, cleanUpdates);
  },

  async deleteNote(id) {
    return await db.notes.delete(id);
  },

  async getAllNotes() {
    return await db.notes.orderBy('updatedAt').reverse().toArray();
  },

  async getNoteById(id) {
    return await db.notes.get(id);
  },

  async clearAllNotes() {
    return await db.notes.clear();
  }
};

export default db;
