import Dexie from 'dexie';

/**
 * 便签数据库类
 * 专门用于管理桌面便签的数据存储
 */
class StickyNotesDB extends Dexie {
  constructor() {
    super('StickyNotesDB');
    
    // 定义数据库版本和表结构
    this.version(1).stores({
      stickyNotes: '++id, noteId, name, content, color, x, y, isDragging, isNew, createdAt, updatedAt'
    });

    this.stickyNotes = this.table('stickyNotes');
  }
}

// 创建数据库实例
const stickyNotesDB = new StickyNotesDB();

/**
 * 便签数据库操作方法
 */
export const stickyNotesDBOperations = {
  
  /**
   * 添加新便签到数据库
   * @param {Object} note - 便签对象
   * @param {string} note.noteId - 便签的唯一标识符
   * @param {string} note.name - 便签名称
   * @param {string} note.content - 便签内容
   * @param {string} note.color - 便签颜色
   * @param {number} note.x - X坐标位置
   * @param {number} note.y - Y坐标位置
   * @param {boolean} note.isDragging - 是否正在拖拽
   * @param {boolean} note.isNew - 是否为新创建的便签
   * @returns {Promise<number>} 返回新增记录的ID
   */
  async addStickyNote(note) {
    try {
      const cleanNote = {
        noteId: note.id, // 使用原始的noteId作为唯一标识
        name: note.name || '',
        content: note.content || '',
        color: note.color || '#fff9c4',
        x: note.x || 0,
        y: note.y || 0,
        isDragging: false, // 存储时总是false
        isNew: false, // 存储时总是false
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await stickyNotesDB.stickyNotes.add(cleanNote);
      console.log('便签已保存到数据库:', cleanNote);
      return result;
    } catch (error) {
      console.error('添加便签到数据库失败:', error);
      throw error;
    }
  },

  /**
   * 更新便签数据
   * @param {string} noteId - 便签的唯一标识符
   * @param {Object} updates - 要更新的字段
   * @returns {Promise<number>} 返回更新的记录数
   */
  async updateStickyNote(noteId, updates) {
    try {
      const cleanUpdates = {
        ...updates,
        isDragging: false, // 存储时总是false
        isNew: false, // 存储时总是false
        updatedAt: new Date()
      };
      
      // 根据noteId查找并更新
      const result = await stickyNotesDB.stickyNotes
        .where('noteId')
        .equals(noteId)
        .modify(cleanUpdates);
      
      console.log('便签已更新:', { noteId, updates: cleanUpdates });
      return result;
    } catch (error) {
      console.error('更新便签失败:', error);
      throw error;
    }
  },

  /**
   * 删除便签
   * @param {string} noteId - 便签的唯一标识符
   * @returns {Promise<number>} 返回删除的记录数
   */
  async deleteStickyNote(noteId) {
    try {
      const result = await stickyNotesDB.stickyNotes
        .where('noteId')
        .equals(noteId)
        .delete();
      
      console.log('便签已从数据库删除:', noteId);
      return result;
    } catch (error) {
      console.error('删除便签失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有便签
   * @returns {Promise<Array>} 返回所有便签数组
   */
  async getAllStickyNotes() {
    try {
      const notes = await stickyNotesDB.stickyNotes
        .orderBy('updatedAt')
        .reverse()
        .toArray();
      
      console.log('从数据库获取所有便签:', notes);
      return notes;
    } catch (error) {
      console.error('获取所有便签失败:', error);
      throw error;
    }
  },

  /**
   * 根据noteId获取便签
   * @param {string} noteId - 便签的唯一标识符
   * @returns {Promise<Object|undefined>} 返回便签对象或undefined
   */
  async getStickyNoteById(noteId) {
    try {
      const note = await stickyNotesDB.stickyNotes
        .where('noteId')
        .equals(noteId)
        .first();
      
      return note;
    } catch (error) {
      console.error('根据ID获取便签失败:', error);
      throw error;
    }
  },

  /**
   * 批量保存便签（用于拖拽完成后批量更新位置）
   * @param {Array} notes - 便签数组
   * @returns {Promise<void>}
   */
  async batchUpdateStickyNotes(notes) {
    try {
      const transaction = stickyNotesDB.transaction('rw', stickyNotesDB.stickyNotes, async () => {
        for (const note of notes) {
          await this.updateStickyNote(note.id, {
            name: note.name,
            content: note.content,
            color: note.color,
            x: note.x,
            y: note.y
          });
        }
      });
      
      await transaction;
      console.log('批量更新便签完成:', notes.length);
    } catch (error) {
      console.error('批量更新便签失败:', error);
      throw error;
    }
  },

  /**
   * 清空所有便签（用于测试或重置）
   * @returns {Promise<void>}
   */
  async clearAllStickyNotes() {
    try {
      await stickyNotesDB.stickyNotes.clear();
      console.log('所有便签已清空');
    } catch (error) {
      console.error('清空便签失败:', error);
      throw error;
    }
  },

  /**
   * 获取便签总数
   * @returns {Promise<number>} 返回便签总数
   */
  async getStickyNotesCount() {
    try {
      const count = await stickyNotesDB.stickyNotes.count();
      return count;
    } catch (error) {
      console.error('获取便签总数失败:', error);
      throw error;
    }
  },

  /**
   * 搜索便签（根据名称或内容）
   * @param {string} keyword - 搜索关键词
   * @returns {Promise<Array>} 返回匹配的便签数组
   */
  async searchStickyNotes(keyword) {
    try {
      if (!keyword || keyword.trim() === '') {
        return await this.getAllStickyNotes();
      }
      
      const notes = await stickyNotesDB.stickyNotes
        .filter(note => 
          note.name.toLowerCase().includes(keyword.toLowerCase()) ||
          note.content.toLowerCase().includes(keyword.toLowerCase())
        )
        .toArray();
      
      console.log('便签搜索结果:', notes);
      return notes;
    } catch (error) {
      console.error('搜索便签失败:', error);
      throw error;
    }
  }
};

// 默认导出数据库实例
export default stickyNotesDB;
