import Dexie from 'dexie';

/**
 * 任务数据库类
 * 专门用于管理ToDoList的任务数据存储
 */
class TodoDB extends Dexie {
  constructor() {
    super('TodoDB');
    
    this.version(1).stores({
      tasks: '++id, title, description, status, createdAt, updatedAt'
    });

    this.tasks = this.table('tasks');
  }
}

// 创建数据库实例
const todoDB = new TodoDB();

/**
 * 任务数据库操作方法
 */
export const todoDBOperations = {
  /**
   * 添加新任务到数据库
   * @param {Object} task - 任务对象
   * @param {string} task.title - 任务标题
   * @param {string} task.description - 任务描述
   * @param {string} task.status - 任务状态
   */
  async addTask(task) {
    try {
      const cleanTask = {
        title: task.title || '',
        description: task.description || '',
        status: task.status || 'todo',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await todoDB.tasks.add(cleanTask);
      console.log('任务已保存到数据库:', cleanTask);
      return result;
    } catch (error) {
      console.error('添加任务到数据库失败:', error);
      throw error;
    }
  },

  /**
   * 更新任务
   * @param {number} id - 任务ID
   * @param {Object} updates - 更新内容
   */
  async updateTask(id, updates) {
    try {
      const cleanUpdates = {
        ...updates,
        updatedAt: new Date()
      };
      
      const result = await todoDB.tasks
        .where('id')
        .equals(id)
        .modify(cleanUpdates);
      
      console.log('任务已更新:', { id, updates: cleanUpdates });
      return result;
    } catch (error) {
      console.error('更新任务失败:', error);
      throw error;
    }
  },

  /**
   * 删除任务
   * @param {number} id - 任务ID
   */
  async deleteTask(id) {
    try {
      const result = await todoDB.tasks
        .where('id')
        .equals(id)
        .delete();
      
      console.log('任务已从数据库删除:', id);
      return result;
    } catch (error) {
      console.error('删除任务失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有任务
   * @returns {Promise<Array>} 任务数组
   */
  async getAllTasks() {
    try {
      const tasks = await todoDB.tasks
        .orderBy('updatedAt')
        .reverse()
        .toArray();
      
      console.log('从数据库获取所有任务:', tasks);
      return tasks;
    } catch (error) {
      console.error('获取所有任务失败:', error);
      throw error;
    }
  },

  /**
   * 根据状态获取任务
   * @param {string} status - 任务状态
   * @returns {Promise<Array>} 任务数组
   */
  async getTasksByStatus(status) {
    try {
      const tasks = await todoDB.tasks
        .where('status')
        .equals(status)
        .toArray();
      
      return tasks;
    } catch (error) {
      console.error('根据状态获取任务失败:', error);
      throw error;
    }
  },

  /**
   * 更改任务状态
   * @param {number} id - 任务ID
   * @param {string} newStatus - 新状态
   */
  async changeTaskStatus(id, newStatus) {
    try {
      const result = await todoDB.tasks
        .where('id')
        .equals(id)
        .modify({
          status: newStatus,
          updatedAt: new Date()
        });
      
      console.log('任务状态已更新:', { id, newStatus });
      return result;
    } catch (error) {
      console.error('更改任务状态失败:', error);
      throw error;
    }
  }
};

// 默认导出数据库实例
export default todoDB;
