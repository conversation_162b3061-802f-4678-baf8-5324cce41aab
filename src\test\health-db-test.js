import { healthDBOperations } from '../lib/healthDB.js'

/**
 * 健康数据库测试文件
 * 用于测试健康数据的增删改查功能
 */

// 测试数据
const testHealthRecord = {
  date: '8.1',
  day: '四',
  meals: [
    { type: '早餐', food: '燕麦粥、鸡蛋' },
    { type: '午饭', food: '鸡胸肉沙拉' },
    { type: '晚上', food: '蒸蛋羹' }
  ],
  exercise: '跑步30分钟',
  weight: '145.5',
  notes: '今天感觉很好，体重有所下降'
}

// 测试函数
async function testHealthDB() {
  console.log('=== 健康数据库测试开始 ===')
  
  try {
    // 1. 测试添加记录
    console.log('1. 测试添加健康记录...')
    const addResult = await healthDBOperations.addHealthRecord(testHealthRecord)
    console.log('添加成功，ID:', addResult)
    
    // 2. 测试获取所有记录
    console.log('2. 测试获取所有记录...')
    const allRecords = await healthDBOperations.getAllHealthRecords()
    console.log('获取到记录数量:', allRecords.length)
    console.log('最新记录:', allRecords[0])
    
    // 3. 测试根据日期获取记录
    console.log('3. 测试根据日期获取记录...')
    const recordByDate = await healthDBOperations.getHealthRecordByDate('8.1')
    console.log('根据日期获取的记录:', recordByDate)
    
    // 4. 测试更新记录
    if (allRecords.length > 0) {
      console.log('4. 测试更新记录...')
      const recordToUpdate = allRecords[0]
      const updateData = {
        weight: '144.8',
        notes: '更新后的备注信息'
      }
      await healthDBOperations.updateHealthRecord(recordToUpdate.id, updateData)
      console.log('更新成功')
      
      // 验证更新
      const updatedRecords = await healthDBOperations.getAllHealthRecords()
      console.log('更新后的记录:', updatedRecords[0])
    }
    
    // 5. 测试体重统计
    console.log('5. 测试体重统计...')
    const weightStats = await healthDBOperations.getWeightStats()
    console.log('体重统计:', weightStats)
    
    // 6. 测试批量导入（可选）
    console.log('6. 测试批量导入...')
    const batchData = [
      {
        date: '8.2',
        day: '五',
        meals: [
          { type: '早餐', food: '全麦面包' },
          { type: '午饭', food: '蔬菜汤' },
          { type: '晚上', food: '水果沙拉' }
        ],
        exercise: '瑜伽45分钟',
        weight: '144.2',
        notes: '今天尝试了新的运动方式'
      },
      {
        date: '8.3',
        day: '六',
        meals: [
          { type: '早餐', food: '酸奶燕麦' },
          { type: '午饭', food: '烤鸡胸肉' },
          { type: '晚上', food: '蒸蔬菜' }
        ],
        exercise: '游泳1小时',
        weight: '143.8',
        notes: '体重持续下降，很开心'
      }
    ]
    
    await healthDBOperations.bulkImportHealthRecords(batchData)
    console.log('批量导入成功')
    
    // 验证批量导入
    const finalRecords = await healthDBOperations.getAllHealthRecords()
    console.log('最终记录数量:', finalRecords.length)
    
    console.log('=== 健康数据库测试完成 ===')
    
  } catch (error) {
    console.error('测试过程中出现错误:', error)
  }
}

// 清理测试数据的函数
async function cleanupTestData() {
  try {
    console.log('清理测试数据...')
    await healthDBOperations.clearAllHealthRecords()
    console.log('测试数据清理完成')
  } catch (error) {
    console.error('清理数据时出现错误:', error)
  }
}

// 导出测试函数
export { testHealthDB, cleanupTestData }

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testHealthDB()
}
