// password-db-test.js
// 将导入路径从相对路径修改为基于项目根目录的路径
import { passwordDB } from '../lib/db.js';  // 添加 .js 扩展名

/**
 * 测试密码数据库操作
 */
async function testPasswordDatabaseOperations() {
  console.log('开始测试密码数据库操作...');

  try {
    // 1. 清空密码数据（测试环境）
    console.log('1. 清空现有密码数据...');
    await passwordDB.deletePassword(1); // 尝试删除示例数据

    // 2. 测试添加密码
    console.log('2. 测试添加密码...');
    const newPassword = {
      name: "公司禅道",
      username: "z<PERSON><PERSON><PERSON>",
      password: "HsoftZZY123",
      url: "http://192.168.28.82/sa-sso/#/hsoft_login",
      notes: "",
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const passwordId = await passwordDB.addPassword(newPassword);
    console.log('添加密码成功，ID:', passwordId);

    // 3. 测试获取所有密码
    console.log('3. 测试获取所有密码...');
    const allPasswords = await passwordDB.getAllPasswords();
    console.log('所有密码:', allPasswords);

    // 4. 测试根据ID获取密码
    console.log('4. 测试根据ID获取密码...');
    const password = await passwordDB.getPasswordById(passwordId);
    console.log('获取的密码:', password);

    // 5. 测试更新密码
    console.log('5. 测试更新密码...');
    await passwordDB.updatePassword(passwordId, {
      ...password,
      name: "更新后的公司禅道",
      password: "更新后的密码",
      updatedAt: new Date()
    });

    const updatedPassword = await passwordDB.getPasswordById(passwordId);
    console.log('更新后的密码:', updatedPassword);

    // 6. 测试搜索密码
    console.log('6. 测试搜索密码...');
    const searchResults = await passwordDB.searchPasswords('公司');
    console.log('搜索结果:', searchResults);

    // 7. 测试删除密码
    console.log('7. 测试删除密码...');
    await passwordDB.deletePassword(passwordId);
    console.log('删除密码成功');

    // 验证删除结果
    const finalPasswords = await passwordDB.getAllPasswords();
    console.log('删除后的所有密码:', finalPasswords);

    console.log('✅ 所有密码数据库操作测试通过！');

  } catch (error) {
    console.error('❌ 密码数据库操作测试失败:', error);
  }
}

// 运行测试
console.log('🚀 开始运行密码数据库测试...');
testPasswordDatabaseOperations();