/**
 * 便签数据库操作测试文件
 * 用于验证便签数据库功能的正确性
 */

import { stickyNotesDBOperations } from '../lib/stickyNotesDB.js';

/**
 * 测试便签数据库基本操作
 */
async function testStickyNotesDatabaseOperations() {
  console.log('开始测试便签数据库操作...');

  try {
    // 1. 清空现有数据（测试环境）
    console.log('1. 清空现有便签数据...');
    await stickyNotesDBOperations.clearAllStickyNotes();

    // 2. 测试添加便签
    console.log('2. 测试添加便签...');
    const testNote1 = {
      id: 'test-note-1',
      name: '测试便签1',
      content: '这是第一个测试便签的内容',
      color: '#fff9c4',
      x: 100,
      y: 100,
      isDragging: false,
      isNew: false
    };

    const noteId1 = await stickyNotesDBOperations.addStickyNote(testNote1);
    console.log('添加便签1成功，数据库ID:', noteId1);

    const testNote2 = {
      id: 'test-note-2',
      name: '测试便签2',
      content: '这是第二个测试便签的内容',
      color: '#ffebee',
      x: 200,
      y: 200,
      isDragging: false,
      isNew: false
    };

    const noteId2 = await stickyNotesDBOperations.addStickyNote(testNote2);
    console.log('添加便签2成功，数据库ID:', noteId2);

    // 3. 测试获取所有便签
    console.log('3. 测试获取所有便签...');
    const allNotes = await stickyNotesDBOperations.getAllStickyNotes();
    console.log('所有便签:', allNotes);

    // 4. 测试根据noteId获取便签
    console.log('4. 测试根据noteId获取便签...');
    const note1 = await stickyNotesDBOperations.getStickyNoteById('test-note-1');
    console.log('获取的便签1:', note1);

    // 5. 测试更新便签
    console.log('5. 测试更新便签...');
    await stickyNotesDBOperations.updateStickyNote('test-note-1', {
      name: '更新后的测试便签1',
      content: '这是更新后的内容',
      color: '#e3f2fd',
      x: 150,
      y: 150
    });

    const updatedNote1 = await stickyNotesDBOperations.getStickyNoteById('test-note-1');
    console.log('更新后的便签1:', updatedNote1);

    // 6. 测试搜索便签
    console.log('6. 测试搜索便签...');
    const searchResults = await stickyNotesDBOperations.searchStickyNotes('测试');
    console.log('搜索结果:', searchResults);

    // 7. 测试获取便签总数
    console.log('7. 测试获取便签总数...');
    const notesCount = await stickyNotesDBOperations.getStickyNotesCount();
    console.log('便签总数:', notesCount);

    // 8. 测试批量更新便签
    console.log('8. 测试批量更新便签...');
    const notesToUpdate = [
      {
        id: 'test-note-1',
        name: '批量更新的便签1',
        content: '批量更新的内容1',
        color: '#e8f5e9',
        x: 300,
        y: 300
      },
      {
        id: 'test-note-2',
        name: '批量更新的便签2',
        content: '批量更新的内容2',
        color: '#f3e5f5',
        x: 400,
        y: 400
      }
    ];

    await stickyNotesDBOperations.batchUpdateStickyNotes(notesToUpdate);
    console.log('批量更新完成');

    // 验证批量更新结果
    const updatedNotes = await stickyNotesDBOperations.getAllStickyNotes();
    console.log('批量更新后的所有便签:', updatedNotes);

    // 9. 测试删除便签
    console.log('9. 测试删除便签...');
    await stickyNotesDBOperations.deleteStickyNote('test-note-1');
    console.log('删除便签1成功');

    // 验证删除结果
    const finalNotes = await stickyNotesDBOperations.getAllStickyNotes();
    console.log('删除后的所有便签:', finalNotes);

    console.log('✅ 所有便签数据库操作测试通过！');

  } catch (error) {
    console.error('❌ 便签数据库操作测试失败:', error);
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n开始测试错误处理...');

  try {
    // 测试获取不存在的便签
    console.log('1. 测试获取不存在的便签...');
    const nonExistentNote = await stickyNotesDBOperations.getStickyNoteById('non-existent-id');
    console.log('不存在的便签结果:', nonExistentNote);

    // 测试删除不存在的便签
    console.log('2. 测试删除不存在的便签...');
    const deleteResult = await stickyNotesDBOperations.deleteStickyNote('non-existent-id');
    console.log('删除不存在便签的结果:', deleteResult);

    // 测试空搜索
    console.log('3. 测试空搜索...');
    const emptySearchResults = await stickyNotesDBOperations.searchStickyNotes('');
    console.log('空搜索结果:', emptySearchResults);

    console.log('✅ 错误处理测试通过！');

  } catch (error) {
    console.error('❌ 错误处理测试失败:', error);
  }
}

/**
 * 测试性能
 */
async function testPerformance() {
  console.log('\n开始测试性能...');

  try {
    // 清空数据
    await stickyNotesDBOperations.clearAllStickyNotes();

    // 批量添加便签测试性能
    console.log('1. 测试批量添加便签性能...');
    const startTime = Date.now();
    
    const batchNotes = [];
    for (let i = 1; i <= 100; i++) {
      batchNotes.push({
        id: `perf-test-note-${i}`,
        name: `性能测试便签${i}`,
        content: `这是第${i}个性能测试便签的内容`,
        color: '#fff9c4',
        x: Math.random() * 800,
        y: Math.random() * 600,
        isDragging: false,
        isNew: false
      });
    }

    // 逐个添加（模拟实际使用场景）
    for (const note of batchNotes) {
      await stickyNotesDBOperations.addStickyNote(note);
    }

    const addTime = Date.now() - startTime;
    console.log(`添加100个便签耗时: ${addTime}ms`);

    // 测试查询性能
    const queryStartTime = Date.now();
    const allNotes = await stickyNotesDBOperations.getAllStickyNotes();
    const queryTime = Date.now() - queryStartTime;
    console.log(`查询所有便签耗时: ${queryTime}ms，结果数量: ${allNotes.length}`);

    // 测试搜索性能
    const searchStartTime = Date.now();
    const searchResults = await stickyNotesDBOperations.searchStickyNotes('性能测试');
    const searchTime = Date.now() - searchStartTime;
    console.log(`搜索便签耗时: ${searchTime}ms，结果数量: ${searchResults.length}`);

    console.log('✅ 性能测试完成！');

  } catch (error) {
    console.error('❌ 性能测试失败:', error);
  }
}

/**
 * 运行所有测试
 */
export async function runAllStickyNotesTests() {
  console.log('🚀 开始运行便签数据库测试套件...\n');

  await testStickyNotesDatabaseOperations();
  await testErrorHandling();
  await testPerformance();

  console.log('\n🎉 所有便签数据库测试完成！');
}

// 如果直接运行此文件，则执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runStickyNotesTests = runAllStickyNotesTests;
  console.log('便签测试函数已挂载到 window.runStickyNotesTests，可在控制台中调用');
}

export default {
  testStickyNotesDatabaseOperations,
  testErrorHandling,
  testPerformance,
  runAllStickyNotesTests
};
